import numpy as np
from scipy.stats import linregress
from .AbstractAkSolver import AbstractAkSolver

"""
A和k的实现求解器
执行log(λ)对δ的OLS回归。
k = -斜率
A = e^截距
"""

class AkRegressionSolver(AbstractAkSolver):
    def __init__(self, spread_specification: np.ndarray):
        """
        @param spread_specification 价差数组（价差-强度曲线的X轴）
        """
        super().__init__(spread_specification)

    def solve_ak(self, intensities: np.ndarray) -> np.ndarray:
        """
        @param intensities 强度数组（价差-强度曲线的Y轴）
        @return 返回估计的A和k数组 [A, k]
        """
        log_intensities = np.log(intensities)
        slope, intercept, _, _, _ = linregress(self.spread_specification, log_intensities)
        return np.array([np.exp(intercept), -slope])
