#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据管理器 - 支持按标的组织的数据文件夹结构
"""

import os
import json
import pandas as pd
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path


@dataclass
class SymbolMetadata:
    """标的元数据"""
    symbol: str
    name: str
    exchange: str
    sector: str = ""
    description: str = ""
    data_start_date: str = ""
    data_end_date: str = ""
    total_records: int = 0
    file_count: int = 0


@dataclass
class DataConfig:
    """数据配置"""
    base_dir: str = "backtest_data"
    default_symbol: str = "ETF_510300"
    train_ratio: float = 0.7
    supported_formats: List[str] = None
    
    def __post_init__(self):
        if self.supported_formats is None:
            self.supported_formats = ['.csv', '.parquet', '.feather']


class DataManager:
    """数据管理器"""
    
    def __init__(self, config: DataConfig = None):
        self.config = config or DataConfig()
        self.base_path = Path(self.config.base_dir)
        self.symbols_cache = {}
        
        # 确保基础目录存在
        self.base_path.mkdir(exist_ok=True)
        
        # 加载或创建全局配置
        self.global_config_path = self.base_path / "config.json"
        self.load_global_config()
    
    def load_global_config(self):
        """加载全局配置"""
        if self.global_config_path.exists():
            with open(self.global_config_path, 'r', encoding='utf-8') as f:
                global_config = json.load(f)
                # 更新配置
                for key, value in global_config.items():
                    if hasattr(self.config, key):
                        setattr(self.config, key, value)
        else:
            self.save_global_config()
    
    def save_global_config(self):
        """保存全局配置"""
        config_dict = asdict(self.config)
        with open(self.global_config_path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)
    
    def get_symbol_path(self, symbol: str) -> Path:
        """获取标的路径"""
        return self.base_path / symbol
    
    def get_symbol_snapshot_path(self, symbol: str) -> Path:
        """获取标的快照数据路径"""
        return self.get_symbol_path(symbol) / "snapshot"

    def get_symbol_trade_path(self, symbol: str) -> Path:
        """获取标的交易数据路径"""
        return self.get_symbol_path(symbol) / "trade"
    
    def get_symbol_merged_path(self, symbol: str) -> Path:
        """获取标的合并数据路径"""
        return self.get_symbol_path(symbol) / "merged"
    
    def get_symbol_metadata_path(self, symbol: str) -> Path:
        """获取标的元数据路径"""
        return self.get_symbol_path(symbol) / "metadata.json"
    
    def create_symbol_structure(self, symbol: str, metadata: SymbolMetadata = None):
        """创建标的文件夹结构"""
        symbol_path = self.get_symbol_path(symbol)
        snapshot_path = self.get_symbol_snapshot_path(symbol)
        trade_path = self.get_symbol_trade_path(symbol)
        merged_path = self.get_symbol_merged_path(symbol)

        # 创建目录
        symbol_path.mkdir(exist_ok=True)
        snapshot_path.mkdir(exist_ok=True)
        trade_path.mkdir(exist_ok=True)
        merged_path.mkdir(exist_ok=True)
        
        # 创建元数据
        if metadata is None:
            metadata = SymbolMetadata(
                symbol=symbol,
                name=symbol,
                exchange="Unknown"
            )
        
        self.save_symbol_metadata(symbol, metadata)
        print(f"✅ 创建标的结构: {symbol}")
    
    def save_symbol_metadata(self, symbol: str, metadata: SymbolMetadata):
        """保存标的元数据"""
        metadata_path = self.get_symbol_metadata_path(symbol)
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(asdict(metadata), f, indent=2, ensure_ascii=False)
    
    def load_symbol_metadata(self, symbol: str) -> Optional[SymbolMetadata]:
        """加载标的元数据"""
        metadata_path = self.get_symbol_metadata_path(symbol)
        if not metadata_path.exists():
            return None
        
        with open(metadata_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            return SymbolMetadata(**data)
    
    def list_symbols(self) -> List[str]:
        """列出所有可用标的"""
        symbols = []
        for item in self.base_path.iterdir():
            if item.is_dir() and not item.name.startswith('.'):
                # 检查是否有metadata.json
                metadata_path = item / "metadata.json"
                if metadata_path.exists():
                    symbols.append(item.name)
        return sorted(symbols)
    
    def get_symbol_info(self, symbol: str) -> Dict:
        """获取标的信息"""
        metadata = self.load_symbol_metadata(symbol)
        if not metadata:
            return {"error": f"标的 {symbol} 不存在"}

        snapshot_path = self.get_symbol_snapshot_path(symbol)
        trade_path = self.get_symbol_trade_path(symbol)
        merged_path = self.get_symbol_merged_path(symbol)

        # 统计文件
        snapshot_files = list(snapshot_path.glob("*"))
        trade_files = list(trade_path.glob("*"))
        merged_files = list(merged_path.glob("*"))

        return {
            "metadata": asdict(metadata),
            "snapshot_files": len(snapshot_files),
            "trade_files": len(trade_files),
            "merged_files": len(merged_files),
            "snapshot_path": str(snapshot_path),
            "trade_path": str(trade_path),
            "merged_path": str(merged_path)
        }
    
    def load_data(self, symbol: str, data_type: str = "merged") -> pd.DataFrame:
        """加载标的数据"""
        if data_type == "merged":
            data_path = self.get_symbol_merged_path(symbol)
        elif data_type == "snapshot":
            data_path = self.get_symbol_snapshot_path(symbol)
        elif data_type == "trade":
            data_path = self.get_symbol_trade_path(symbol)
        else:
            raise ValueError(f"不支持的数据类型: {data_type}，支持: merged, snapshot, trade")

        if not data_path.exists():
            raise FileNotFoundError(f"数据路径不存在: {data_path}")
        
        # 查找数据文件
        data_files = []
        for ext in self.config.supported_formats:
            data_files.extend(list(data_path.glob(f"*{ext}")))
        
        if not data_files:
            raise FileNotFoundError(f"在 {data_path} 中没有找到数据文件")
        
        # 加载并合并数据
        all_data = []
        for file_path in sorted(data_files):
            if file_path.suffix == '.csv':
                df = pd.read_csv(file_path)
            elif file_path.suffix == '.parquet':
                df = pd.read_parquet(file_path)
            elif file_path.suffix == '.feather':
                df = pd.read_feather(file_path)
            else:
                continue
            
            all_data.append(df)
        
        if not all_data:
            raise ValueError("没有成功加载任何数据文件")
        
        # 合并数据
        combined_data = pd.concat(all_data, ignore_index=True)
        
        # 按时间戳排序
        if 'timestamp' in combined_data.columns:
            combined_data = combined_data.sort_values('timestamp').reset_index(drop=True)
        
        print(f"✅ 加载 {symbol} 数据: {len(combined_data)} 行")
        return combined_data
    
    def split_data(self, symbol: str, train_ratio: float = None) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """分割训练和测试数据"""
        if train_ratio is None:
            train_ratio = self.config.train_ratio
        
        data = self.load_data(symbol, "merged")
        split_idx = int(len(data) * train_ratio)
        
        train_data = data.iloc[:split_idx].copy()
        test_data = data.iloc[split_idx:].copy()
        
        print(f"📊 数据分割 {symbol}:")
        print(f"  训练集: {len(train_data)} 行 ({train_ratio*100:.1f}%)")
        print(f"  测试集: {len(test_data)} 行 ({(1-train_ratio)*100:.1f}%)")
        
        return train_data, test_data
    
    def migrate_old_data(self, old_data_dir: str, symbol: str, symbol_metadata: SymbolMetadata = None):
        """迁移旧数据到新结构"""
        old_path = Path(old_data_dir)
        if not old_path.exists():
            raise FileNotFoundError(f"旧数据目录不存在: {old_data_dir}")
        
        # 创建新结构
        self.create_symbol_structure(symbol, symbol_metadata)
        
        # 复制文件到merged目录
        merged_path = self.get_symbol_merged_path(symbol)
        
        copied_files = 0
        for file_path in old_path.glob("*.csv"):
            target_path = merged_path / file_path.name
            import shutil
            shutil.copy2(file_path, target_path)
            copied_files += 1
        
        print(f"✅ 迁移完成: 复制了 {copied_files} 个文件到 {symbol}")
        
        # 更新元数据
        if symbol_metadata:
            symbol_metadata.file_count = copied_files
            self.save_symbol_metadata(symbol, symbol_metadata)
    
    def update_metadata_stats(self, symbol: str):
        """更新标的统计信息"""
        metadata = self.load_symbol_metadata(symbol)
        if not metadata:
            return
        
        try:
            data = self.load_data(symbol, "merged")
            metadata.total_records = len(data)
            
            if 'timestamp' in data.columns:
                timestamps = pd.to_datetime(data['timestamp'], unit='ms')
                metadata.data_start_date = timestamps.min().strftime('%Y-%m-%d')
                metadata.data_end_date = timestamps.max().strftime('%Y-%m-%d')
            
            merged_path = self.get_symbol_merged_path(symbol)
            metadata.file_count = len(list(merged_path.glob("*")))
            
            self.save_symbol_metadata(symbol, metadata)
            print(f"✅ 更新 {symbol} 元数据统计")
            
        except Exception as e:
            print(f"❌ 更新 {symbol} 元数据失败: {e}")


# 预定义的标的信息
PREDEFINED_SYMBOLS = {
    "ETF_510300": SymbolMetadata(
        symbol="ETF_510300",
        name="沪深300ETF",
        exchange="SSE",
        sector="ETF",
        description="跟踪沪深300指数的ETF"
    ),
    "ETF_510500": SymbolMetadata(
        symbol="ETF_510500",
        name="中证500ETF",
        exchange="SSE",
        sector="ETF",
        description="跟踪中证500指数的ETF"
    ),
    "ETF_159915": SymbolMetadata(
        symbol="ETF_159915",
        name="创业板ETF",
        exchange="SZSE",
        sector="ETF",
        description="跟踪创业板指数的ETF"
    ),
    "ETF_159919": SymbolMetadata(
        symbol="ETF_159919",
        name="沪深300ETF",
        exchange="SZSE",
        sector="ETF",
        description="跟踪沪深300指数的ETF(深交所)"
    ),
}


def create_default_structure():
    """创建默认的数据结构"""
    manager = DataManager()
    
    print("🏗️ 创建默认数据结构...")
    for symbol, metadata in PREDEFINED_SYMBOLS.items():
        manager.create_symbol_structure(symbol, metadata)
    
    print(f"✅ 默认结构创建完成")
    return manager
