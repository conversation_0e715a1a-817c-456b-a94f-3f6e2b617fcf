#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU深度学习CTA策略
基于GPU_CTA_Inference.py的回测策略实现
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import os
import sys
from typing import Dict, Tuple, Optional, Any
from dataclasses import dataclass

# 添加CTA路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'CTA'))

from strategy_base import StrategyBase, Signal, strategy_manager
from CTA.UniversalFactorCalculator import UniversalFactorCalculator


@dataclass
class GPUCTAConfig:
    """GPU CTA策略配置"""
    # 模型参数
    model_path: str = "CTA/best_gpu_cta_model.pth"
    seq_len: int = 60  # 序列长度
    
    # 交易参数
    signal_threshold: float = 0.4  # 信号阈值
    position_size: int = 5000  # 每次交易数量
    
    # 数据处理参数
    resample_freq: str = "2min"  # 重采样频率
    rolling_window: int = 100  # 滚动标准化窗口
    
    # 风险控制
    max_position: int = 20000  # 最大持仓
    stop_loss_ratio: float = 0.02  # 止损比例
    take_profit_ratio: float = 0.04  # 止盈比例


class AdvancedCTAModel(nn.Module):
    """GPU训练的高级CTA模型架构 - 与推理脚本保持一致"""
    
    def __init__(self, input_size, hidden_size=128, num_layers=3, dropout=0.2):
        super(AdvancedCTAModel, self).__init__()
        
        # CNN特征提取
        self.feature_extractor = nn.Sequential(
            nn.Conv1d(input_size, 64, kernel_size=3, padding=1),
            nn.BatchNorm1d(64),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),
            
            nn.Conv1d(64, 128, kernel_size=3, padding=1),
            nn.BatchNorm1d(128),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),
            
            nn.Conv1d(128, hidden_size, kernel_size=3, padding=1),
            nn.BatchNorm1d(hidden_size),
            nn.ReLU(inplace=True)
        )
        
        # 双向LSTM
        self.lstm = nn.LSTM(
            input_size=hidden_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0,
            bidirectional=True
        )
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_size * 2,
            num_heads=8,
            dropout=dropout,
            batch_first=True
        )
        
        # 输出层
        self.classifier = nn.Sequential(
            nn.Linear(hidden_size * 2, hidden_size),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),
            nn.Linear(hidden_size // 2, 1),
            nn.Tanh()
        )
    
    def forward(self, x):
        batch_size, seq_len, features = x.size()
        
        # CNN特征提取
        x = x.transpose(1, 2)
        x = self.feature_extractor(x)
        x = x.transpose(1, 2)
        
        # 双向LSTM
        lstm_out, _ = self.lstm(x)
        
        # 注意力机制
        attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)
        
        # 特征融合
        pooled = torch.mean(attn_out, dim=1)
        last_step = attn_out[:, -1, :]
        combined = pooled + last_step
        
        # 输出
        output = self.classifier(combined)
        return output


class GPUCTAStrategy(StrategyBase):
    """GPU深度学习CTA策略"""
    
    def __init__(self, config: GPUCTAConfig):
        super().__init__(config)

        # 如果传入的是字典，转换为配置对象
        if isinstance(config, dict):
            gpu_config = GPUCTAConfig()
            for key, value in config.items():
                if hasattr(gpu_config, key):
                    setattr(gpu_config, key, value)
            self.config = gpu_config
        else:
            self.config = config
        
        # 初始化组件
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.factor_calculator = UniversalFactorCalculator()
        self.model = None
        
        # 数据缓存
        self.data_buffer = []
        self.factor_buffer = []
        self.normalized_buffer = []
        
        # 状态变量
        self.initialized = False
        self.last_prediction = None
        self.last_signal = 0
        
        # 持仓状态
        self.position_quantity = 0
        self.position_avg_price = 0.0
        self.entry_price = 0.0
        
        print(f"🤖 GPU CTA策略初始化")
        print(f"📱 设备: {self.device}")
        print(f"🎯 信号阈值: {self.config.signal_threshold}")
    
    def _load_model(self):
        """加载GPU训练的模型"""
        if not os.path.exists(self.config.model_path):
            raise FileNotFoundError(f"模型文件不存在: {self.config.model_path}")

        # 加载检查点
        checkpoint = torch.load(self.config.model_path, map_location=self.device)

        # 从模型权重推断参数
        first_conv = checkpoint['feature_extractor.0.weight']
        expected_input_size = first_conv.shape[1]

        print(f"🔧 模型期望输入维度: {expected_input_size}")

        # 初始化模型
        model = AdvancedCTAModel(
            input_size=expected_input_size,
            hidden_size=128,
            num_layers=3,
            dropout=0.2
        ).to(self.device)

        # 加载权重
        model.load_state_dict(checkpoint)
        model.eval()

        print(f"✅ 模型加载成功")
        print(f"🧠 参数数量: {sum(p.numel() for p in model.parameters()):,}")

        # 存储期望的输入维度
        self.expected_input_size = expected_input_size

        return model
    
    def initialize(self, historical_data: pd.DataFrame):
        """策略初始化"""
        print(f"🔄 GPU CTA策略初始化...")
        print(f"📊 历史数据: {len(historical_data)} 行")
        
        try:
            # 加载模型
            self.model = self._load_model()
            
            # 预处理历史数据
            processed_data = self._preprocess_data(historical_data)
            print(f"📊 预处理后数据: {len(processed_data)} 行")
            
            # 计算历史因子
            if len(processed_data) > 0:
                factors = self.factor_calculator.calculate_all_factors(processed_data)
                print(f"📊 历史因子: {factors.shape}")

                # 扩展因子到目标维度
                if hasattr(self, 'expected_input_size'):
                    factors = self._expand_factors_to_target_size(factors, self.expected_input_size)
                    print(f"📊 扩展后因子: {factors.shape}")

                # 初始化缓存
                self.data_buffer = processed_data.tail(self.config.seq_len).to_dict('records')
                self.factor_buffer = factors.tail(self.config.seq_len).values.tolist()

                # 初始化标准化
                if len(self.factor_buffer) > 0:
                    factor_array = np.array(self.factor_buffer)
                    self.normalized_buffer = self._rolling_normalize(factor_array).tolist()
            
            self.initialized = True
            print(f"✅ GPU CTA策略初始化完成")
            
        except Exception as e:
            print(f"❌ GPU CTA策略初始化失败: {e}")
            self.initialized = False
    
    def _preprocess_data(self, raw_data: pd.DataFrame) -> pd.DataFrame:
        """数据预处理 - 与推理脚本保持一致"""
        # 提取snapshot数据
        if 'event_type' in raw_data.columns:
            snapshot_data = raw_data[raw_data['event_type'] == 'snapshot'].copy()
        else:
            snapshot_data = raw_data.copy()
        
        if len(snapshot_data) == 0:
            return pd.DataFrame()
        
        # 时间戳处理
        if 'timestamp' in snapshot_data.columns:
            snapshot_data['datetime'] = pd.to_datetime(snapshot_data['timestamp'], unit='ms')
            snapshot_data = snapshot_data.set_index('datetime').sort_index()
            
            # 重采样到指定频率
            agg_dict = {
                'bid_price': 'last', 'ask_price': 'last',
                'bid_vol': 'mean', 'ask_vol': 'mean'
            }
            
            # 添加5档数据
            for i in range(1, 6):
                agg_dict[f'bid_price_{i}'] = 'last'
                agg_dict[f'ask_price_{i}'] = 'last'
                agg_dict[f'bid_vol_{i}'] = 'mean'
                agg_dict[f'ask_vol_{i}'] = 'mean'
            
            resampled = snapshot_data.resample(self.config.resample_freq).agg(agg_dict).dropna()
            resampled['event_type'] = 'snapshot'
            
            return resampled
        else:
            return snapshot_data
    
    def _rolling_normalize(self, data: np.ndarray) -> np.ndarray:
        """滚动标准化 - 与推理脚本保持一致"""
        normalized = np.zeros_like(data)
        window = self.config.rolling_window
        
        for i in range(len(data)):
            start_idx = max(0, i - window)
            end_idx = i + 1
            
            window_data = data[start_idx:end_idx]
            mean_val = np.mean(window_data, axis=0)
            std_val = np.std(window_data, axis=0) + 1e-8
            
            normalized[i] = (data[i] - mean_val) / std_val
        
        return normalized

    def _expand_factors_to_target_size(self, factors: pd.DataFrame, target_size: int) -> pd.DataFrame:
        """扩展因子到目标维度"""
        current_size = factors.shape[1]

        if current_size == target_size:
            return factors

        print(f"🔧 扩展因子: {current_size} -> {target_size}")

        if current_size > target_size:
            # 如果因子太多，选择前target_size个
            return factors.iloc[:, :target_size]

        # 如果因子不够，需要扩展
        expanded_factors = factors.copy()

        # 方法1: 添加因子的滞后版本
        lag_periods = [1, 2, 3, 5, 10]
        for lag in lag_periods:
            if expanded_factors.shape[1] >= target_size:
                break
            for col in factors.columns:
                if expanded_factors.shape[1] >= target_size:
                    break
                lag_col = f"{col}_lag{lag}"
                expanded_factors[lag_col] = factors[col].shift(lag)

        # 方法2: 添加因子的移动平均
        ma_periods = [3, 5, 10, 20]
        for period in ma_periods:
            if expanded_factors.shape[1] >= target_size:
                break
            for col in factors.columns:
                if expanded_factors.shape[1] >= target_size:
                    break
                ma_col = f"{col}_ma{period}"
                expanded_factors[ma_col] = factors[col].rolling(period).mean()

        # 方法3: 添加因子的差分
        for col in factors.columns:
            if expanded_factors.shape[1] >= target_size:
                break
            diff_col = f"{col}_diff"
            expanded_factors[diff_col] = factors[col].diff()

        # 方法4: 添加因子的标准化版本
        for col in factors.columns:
            if expanded_factors.shape[1] >= target_size:
                break
            std_col = f"{col}_std"
            expanded_factors[std_col] = (factors[col] - factors[col].rolling(20).mean()) / factors[col].rolling(20).std()

        # 方法5: 如果还不够，用随机噪声填充（不推荐，但确保维度匹配）
        while expanded_factors.shape[1] < target_size:
            noise_col = f"noise_{expanded_factors.shape[1]}"
            expanded_factors[noise_col] = np.random.normal(0, 0.01, len(expanded_factors))

        # 确保正好是目标维度
        if expanded_factors.shape[1] > target_size:
            expanded_factors = expanded_factors.iloc[:, :target_size]

        # 填充NaN值
        expanded_factors = expanded_factors.fillna(method='ffill').fillna(0)

        print(f"✅ 因子扩展完成: {expanded_factors.shape}")
        return expanded_factors
    
    def _generate_prediction(self) -> Optional[float]:
        """生成模型预测"""
        if not self.initialized or self.model is None:
            return None
        
        if len(self.normalized_buffer) < self.config.seq_len:
            return None
        
        try:
            # 构建输入序列
            input_seq = np.array(self.normalized_buffer[-self.config.seq_len:])
            input_tensor = torch.FloatTensor(input_seq).unsqueeze(0).to(self.device)
            
            # 预测
            with torch.no_grad():
                prediction = self.model(input_tensor)
                pred_value = prediction.cpu().numpy()[0, 0]
            
            return float(pred_value)
            
        except Exception as e:
            print(f"❌ 预测失败: {e}")
            return None
    
    def _generate_signal(self, prediction: float) -> Tuple[int, str]:
        """生成交易信号 - 与推理脚本保持一致"""
        if prediction is None:
            return 0, "无预测"
        
        threshold = self.config.signal_threshold
        
        if prediction > threshold:
            return 1, f"🟢 做多 ({prediction:.4f})"
        elif prediction < -threshold:
            return -1, f"🔴 做空 ({prediction:.4f})"
        else:
            return 0, f"⚪ 观望 ({prediction:.4f})"
    
    def on_market_data(self, market_data) -> Optional[Signal]:
        """处理市场数据并生成交易信号"""
        if not self.initialized:
            return None

        try:
            # 构建数据行
            data_row = {
                'timestamp': market_data.timestamp,
                'bid_price': market_data.bid_price,
                'ask_price': market_data.ask_price,
                'bid_vol': market_data.bid_volume,
                'ask_vol': market_data.ask_volume,
                'event_type': market_data.extra_data.get('event_type', 'snapshot')
            }

            # 添加5档数据
            if market_data.bid_prices and market_data.ask_prices:
                for i in range(min(5, len(market_data.bid_prices))):
                    data_row[f'bid_price_{i+1}'] = market_data.bid_prices[i]
                    data_row[f'ask_price_{i+1}'] = market_data.ask_prices[i]
                if market_data.bid_volumes and market_data.ask_volumes:
                    for i in range(min(5, len(market_data.bid_volumes))):
                        data_row[f'bid_vol_{i+1}'] = market_data.bid_volumes[i]
                        data_row[f'ask_vol_{i+1}'] = market_data.ask_volumes[i]

            # 只处理snapshot数据
            if data_row['event_type'] != 'snapshot':
                return None

            # 更新数据缓存
            self.data_buffer.append(data_row)
            if len(self.data_buffer) > self.config.seq_len * 2:  # 保留更多数据用于重采样
                self.data_buffer = self.data_buffer[-self.config.seq_len * 2:]

            # 需要足够的数据才能进行预测
            if len(self.data_buffer) < self.config.seq_len:
                return None

            # 每隔一定时间更新因子和预测
            if len(self.data_buffer) % 10 == 0:  # 每10个数据点更新一次
                return self._update_prediction_and_signal(market_data)

            # 检查止盈止损
            if self.position_quantity != 0:
                return self._check_stop_conditions(market_data)

            return None

        except Exception as e:
            print(f"❌ 处理市场数据失败: {e}")
            return None

    def _update_prediction_and_signal(self, market_data) -> Optional[Signal]:
        """更新预测和信号"""
        try:
            # 转换为DataFrame进行因子计算
            df = pd.DataFrame(self.data_buffer)

            # 预处理数据
            processed_data = self._preprocess_data(df)
            if len(processed_data) == 0:
                return None

            # 计算因子
            factors = self.factor_calculator.calculate_all_factors(processed_data)
            if len(factors) == 0:
                return None

            # 扩展因子到目标维度（如果模型已加载）
            if hasattr(self, 'expected_input_size'):
                factors = self._expand_factors_to_target_size(factors, self.expected_input_size)

            # 更新因子缓存
            new_factors = factors.tail(1).values.tolist()
            self.factor_buffer.extend(new_factors)
            if len(self.factor_buffer) > self.config.seq_len * 2:
                self.factor_buffer = self.factor_buffer[-self.config.seq_len * 2:]

            # 更新标准化缓存
            if len(self.factor_buffer) >= self.config.seq_len:
                factor_array = np.array(self.factor_buffer)
                normalized = self._rolling_normalize(factor_array)
                self.normalized_buffer = normalized.tolist()

            # 生成预测
            prediction = self._generate_prediction()
            if prediction is None:
                return None

            self.last_prediction = prediction
            signal, signal_msg = self._generate_signal(prediction)
            self.last_signal = signal

            # 生成交易信号
            return self._create_trading_signal(signal, market_data, signal_msg)

        except Exception as e:
            print(f"❌ 更新预测失败: {e}")
            return None

    def _create_trading_signal(self, signal: int, market_data, reason: str) -> Optional[Signal]:
        """创建交易信号"""
        if signal == 0:  # 观望
            return None

        # 检查持仓限制
        if signal == 1 and self.position_quantity >= self.config.max_position:
            return None
        if signal == -1 and self.position_quantity <= -self.config.max_position:
            return None

        # 确定交易方向和价格
        if signal == 1:  # 做多
            if self.position_quantity >= 0:  # 当前无空头持仓
                action = "buy"
                price = market_data.ask_price
                quantity = self.config.position_size
            else:  # 平空头
                action = "buy"
                price = market_data.ask_price
                quantity = min(abs(self.position_quantity), self.config.position_size)
        else:  # 做空
            if self.position_quantity <= 0:  # 当前无多头持仓
                action = "sell"
                price = market_data.bid_price
                quantity = self.config.position_size
            else:  # 平多头
                action = "sell"
                price = market_data.bid_price
                quantity = min(self.position_quantity, self.config.position_size)

        return Signal(
            action=action,
            quantity=quantity,
            price=price,
            reason=reason
        )

    def _check_stop_conditions(self, market_data) -> Optional[Signal]:
        """检查止盈止损条件"""
        if self.position_quantity == 0 or self.entry_price == 0:
            return None

        current_price = market_data.last_price

        if self.position_quantity > 0:  # 多头持仓
            # 止损
            if current_price <= self.entry_price * (1 - self.config.stop_loss_ratio):
                return Signal(
                    action="sell",
                    quantity=self.position_quantity,
                    price=market_data.bid_price,
                    reason=f"止损 (价格: {current_price:.6f}, 入场: {self.entry_price:.6f})"
                )
            # 止盈
            elif current_price >= self.entry_price * (1 + self.config.take_profit_ratio):
                return Signal(
                    action="sell",
                    quantity=self.position_quantity,
                    price=market_data.bid_price,
                    reason=f"止盈 (价格: {current_price:.6f}, 入场: {self.entry_price:.6f})"
                )
        else:  # 空头持仓
            # 止损
            if current_price >= self.entry_price * (1 + self.config.stop_loss_ratio):
                return Signal(
                    action="buy",
                    quantity=abs(self.position_quantity),
                    price=market_data.ask_price,
                    reason=f"止损 (价格: {current_price:.6f}, 入场: {self.entry_price:.6f})"
                )
            # 止盈
            elif current_price <= self.entry_price * (1 - self.config.take_profit_ratio):
                return Signal(
                    action="buy",
                    quantity=abs(self.position_quantity),
                    price=market_data.ask_price,
                    reason=f"止盈 (价格: {current_price:.6f}, 入场: {self.entry_price:.6f})"
                )

        return None

    def on_trade_executed(self, price: float, quantity: int, side: str, timestamp: int):
        """交易执行回调"""
        if side == "buy":
            if self.position_quantity <= 0:  # 开多或平空
                if self.position_quantity == 0:  # 开多
                    self.entry_price = price
                self.position_quantity += quantity
                if self.position_quantity > 0:
                    self.position_avg_price = price
        else:  # sell
            if self.position_quantity >= 0:  # 开空或平多
                if self.position_quantity == 0:  # 开空
                    self.entry_price = price
                self.position_quantity -= quantity
                if self.position_quantity < 0:
                    self.position_avg_price = price

        # 如果持仓归零，重置入场价格
        if self.position_quantity == 0:
            self.entry_price = 0.0
            self.position_avg_price = 0.0

    def get_optimization_params(self) -> Dict[str, Tuple[float, float]]:
        """获取优化参数范围"""
        return {
            'signal_threshold': (0.2, 0.8),
            'position_size': (1000, 10000),
            'stop_loss_ratio': (0.01, 0.05),
            'take_profit_ratio': (0.02, 0.08),
        }

    def update_params(self, params: Dict[str, Any]) -> None:
        """更新策略参数（用于优化）"""
        for key, value in params.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                print(f"🔧 更新参数: {key} = {value}")

    def update_params(self, params: Dict[str, Any]) -> None:
        """更新策略参数（用于优化）"""
        for key, value in params.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                print(f"🔧 更新参数: {key} = {value}")


# 注册策略
strategy_manager.register_strategy('gpu_cta', GPUCTAStrategy, GPUCTAConfig)
