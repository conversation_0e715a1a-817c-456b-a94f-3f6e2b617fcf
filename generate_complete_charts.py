#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成完整的优化分析图表
包括参数分析和PnL曲线图
"""

import optuna
import matplotlib.pyplot as plt
import matplotlib
import pandas as pd
import numpy as np
import os
import json
from backtest_engine import BacktestEngine, BacktestConfig, load_data
from strategy_base import strategy_manager
import strategy_qmt_mm

# 设置字体
matplotlib.rcParams['font.family'] = 'sans-serif'
matplotlib.rcParams['axes.unicode_minus'] = False

def load_optimization_results():
    """加载优化结果"""
    try:
        study = optuna.load_study(
            study_name='qmt_mm_optimization',
            storage='sqlite:///optuna_studies/qmt_mm_optimization.db'
        )
        return study
    except Exception as e:
        print(f"❌ 加载优化结果失败: {e}")
        return None

def load_best_params():
    """加载最佳参数"""
    try:
        # 首先尝试从 optimization_results_qmt_mm.json 加载
        with open('optimization_results/optimization_results_qmt_mm.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config['best_params']
    except:
        try:
            with open('optimization_results/qmt_mm_best_config_new.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            return config['best_params']
        except:
            try:
                with open('optimization_results/qmt_mm_best_config.json', 'r', encoding='utf-8') as f:
                    config = json.load(f)
                return config['best_params']
            except:
                print("⚠️ 未找到最佳配置，使用默认参数")
                return {}

def generate_optimization_charts(study):
    """生成优化分析图表"""
    print("📊 生成优化分析图表...")
    
    # 确保目录存在
    os.makedirs('optimization_results', exist_ok=True)
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('QMT Strategy Optimization Analysis', fontsize=16, fontweight='bold')
    
    # 获取完成的试验数据
    trials_df = study.trials_dataframe()
    completed_trials = trials_df[trials_df['state'] == 'COMPLETE']
    
    if len(completed_trials) == 0:
        print("⚠️ 没有完成的试验数据")
        return
    
    print(f"📊 分析 {len(completed_trials)} 个完成的试验")
    
    # 1. 优化历史
    trial_numbers = completed_trials['number'].values
    trial_values = completed_trials['value'].values
    
    # 绘制所有试验点
    axes[0, 0].scatter(trial_numbers, trial_values, alpha=0.6, s=50, c='lightblue', label='Trial Points')
    
    # 绘制最佳值曲线
    best_values = []
    current_best = float('-inf')
    for value in trial_values:
        if value > current_best:
            current_best = value
        best_values.append(current_best)
    
    axes[0, 0].plot(trial_numbers, best_values, 'r-', linewidth=2, label='Best Value')
    axes[0, 0].set_xlabel('Trial Number')
    axes[0, 0].set_ylabel('Objective Value (Sharpe + Calmar)')
    axes[0, 0].set_title('Optimization History')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 参数重要性
    param_cols = [col for col in completed_trials.columns if col.startswith('params_')]
    importances = {}
    
    for param_col in param_cols:
        param_name = param_col.replace('params_', '')
        correlation = abs(completed_trials[param_col].corr(completed_trials['value']))
        if not np.isnan(correlation):
            importances[param_name] = correlation
    
    if importances:
        params = list(importances.keys())
        values = list(importances.values())
        
        bars = axes[0, 1].barh(params, values, color='skyblue')
        axes[0, 1].set_xlabel('Correlation (Absolute Value)')
        axes[0, 1].set_title('Parameter Importance')
        axes[0, 1].grid(True, alpha=0.3, axis='x')
        
        # 添加数值标签
        for bar, value in zip(bars, values):
            axes[0, 1].text(bar.get_width() + 0.01, bar.get_y() + bar.get_height()/2, 
                    f'{value:.3f}', va='center', fontsize=9)
    
    # 3. 试验值分布
    axes[1, 0].hist(trial_values, bins=min(20, len(trial_values)//2), alpha=0.7, color='lightgreen', edgecolor='black')
    axes[1, 0].axvline(study.best_value, color='red', linestyle='--', linewidth=2, 
               label=f'Best: {study.best_value:.6f}')
    axes[1, 0].set_xlabel('Objective Value')
    axes[1, 0].set_ylabel('Frequency')
    axes[1, 0].set_title('Trial Values Distribution')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 4. 前5个最佳试验
    axes[1, 1].axis('off')
    sorted_trials = sorted([t for t in study.trials if t.value is not None], 
                          key=lambda x: x.value, reverse=True)
    
    top5_text = 'Top 5 Best Trials:\n\n'
    for i, trial in enumerate(sorted_trials[:5]):
        top5_text += f'#{i+1}: Score {trial.value:.6f}\n'
    
    axes[1, 1].text(0.1, 0.9, top5_text, transform=axes[1, 1].transAxes, fontsize=12,
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))
    axes[1, 1].set_title('Best Trials Summary')
    
    plt.tight_layout()
    
    # 保存图表
    save_path = 'optimization_results/optimization_analysis.png'
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f'✅ Optimization analysis saved: {save_path}')
    
    plt.close()

def generate_pnl_charts(backtest_results=None, instrument_id="ETF_513120"):
    """生成PnL曲线图"""
    print("📈 生成PnL曲线图...")
    
    # 加载最佳参数
    best_params = load_best_params()
    print(f"📊 使用最佳参数: {len(best_params)} 个")
    
    # 加载数据
    data = load_data("backtest_data", instrument_id, snapshot_only=True)
    print(f"📊 数据量: {len(data)} 行")
    
    # 创建策略
    strategy = strategy_manager.create_strategy('qmt_mm', best_params)
    
    # 创建回测配置
    config = BacktestConfig()
    config.initial_cash = 1000000.0
    config.commission_rate = 0.00005  # 万0.5手续费
    config.snapshot_only = True
    
    # 运行回测
    engine = BacktestEngine(config, strategy)
    if backtest_results is None:
        results = engine.run_backtest(data)
    else:
        results = backtest_results
    
    print(f"✅ 回测完成")
    print(f"   总收益率: {results.get('total_return', 0)*100:.2f}%")
    print(f"   夏普比率: {results.get('sharpe_ratio', 0):.4f}")
    print(f"   卡尔玛比率: {results.get('calmar_ratio', 0):.4f}")
    print(f"   索提诺比率: {results.get('sortino_ratio', 0):.4f}")
    print(f"   总交易次数: {results.get('total_trades', 0)}")

    # 交易数据统计
    trades_debug = results.get('trades', [])
    print(f"   交易数据: {len(trades_debug)} 笔")
    
    # 获取权益曲线数据
    equity_curve = results.get('equity_curve', [])
    if not equity_curve:
        print("⚠️ 没有权益曲线数据")
        return
    
    # 转换权益曲线数据
    equity_data = []
    for item in equity_curve:
        if isinstance(item, tuple) and len(item) == 2:
            timestamp, equity = item
            equity_data.append({'timestamp': timestamp, 'equity': equity})
        elif isinstance(item, dict):
            equity_data.append(item)
    
    if not equity_data:
        print("⚠️ 权益数据格式错误")
        return
    
    equity_df = pd.DataFrame(equity_data)
    equity_df['datetime'] = pd.to_datetime(equity_df['timestamp'], unit='ms')
    equity_df = equity_df.set_index('datetime')
    
    # 创建PnL图表 - 改为2x3布局以添加更多图表
    fig, axes = plt.subplots(2, 3, figsize=(20, 12))
    fig.suptitle('QMT Strategy PnL Analysis (Best Parameters)', fontsize=16, fontweight='bold')
    
    # 1. 权益曲线
    axes[0, 0].plot(equity_df.index, equity_df['equity'], 'b-', linewidth=2, label='Total Equity')
    axes[0, 0].axhline(y=1000000, color='r', linestyle='--', alpha=0.7, label='Initial Capital')
    axes[0, 0].set_title('Equity Curve')
    axes[0, 0].set_ylabel('Equity (CNY)')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    axes[0, 0].tick_params(axis='x', rotation=45)
    
    # 2. 收益率曲线
    returns = equity_df['equity'].pct_change().fillna(0)
    cumulative_returns = (1 + returns).cumprod() - 1
    axes[0, 1].plot(equity_df.index, cumulative_returns * 100, 'g-', linewidth=2)
    axes[0, 1].set_title('Cumulative Returns')
    axes[0, 1].set_ylabel('Returns (%)')
    axes[0, 1].grid(True, alpha=0.3)
    axes[0, 1].tick_params(axis='x', rotation=45)
    
    # 3. 回撤分析 - 移到axes[0, 2]
    peak = equity_df['equity'].expanding().max()
    drawdown = (equity_df['equity'] - peak) / peak
    axes[0, 2].fill_between(equity_df.index, drawdown * 100, 0, color='red', alpha=0.3)
    axes[0, 2].plot(equity_df.index, drawdown * 100, 'r-', linewidth=1)
    axes[0, 2].set_title('Drawdown Analysis')
    axes[0, 2].set_ylabel('Drawdown (%)')
    axes[0, 2].grid(True, alpha=0.3)
    axes[0, 2].tick_params(axis='x', rotation=45)
    
    # 4. PnL随交易次数变化 - 只显示平仓交易
    trades = results.get('trades', [])
    if trades:
        # 只统计平仓交易（卖出）的PnL
        closing_trades = []
        for trade in trades:
            if isinstance(trade, dict):
                side = trade.get('side', '')
            elif hasattr(trade, 'side'):
                side = trade.side
            else:
                side = ''

            if side == 'sell':  # 只要平仓交易
                closing_trades.append(trade)

        # 计算累计PnL
        trade_pnls = []
        cumulative_pnl = 0

        for i, trade in enumerate(closing_trades):
            if isinstance(trade, dict) and 'pnl' in trade:
                pnl = trade['pnl']
            elif hasattr(trade, 'pnl'):
                pnl = trade.pnl
            elif hasattr(trade, '__dict__') and 'pnl' in trade.__dict__:
                pnl = trade.__dict__['pnl']
            else:
                pnl = 0

            cumulative_pnl += pnl
            trade_pnls.append(cumulative_pnl)

        if len(trade_pnls) > 0:
            print(f"   最终累计PnL: {trade_pnls[-1]:.2f}元")

        if trade_pnls:
            trade_numbers = range(1, len(trade_pnls) + 1)
            axes[1, 0].plot(trade_numbers, trade_pnls, 'b-', linewidth=2, marker='o', markersize=2)
            axes[1, 0].axhline(y=0, color='r', linestyle='--', alpha=0.7, label='Break Even')
            axes[1, 0].set_title('Cumulative PnL by Closing Trade Number')
            axes[1, 0].set_xlabel('Closing Trade Number (Sell Only)')
            axes[1, 0].set_ylabel('Cumulative PnL (CNY)')
            axes[1, 0].grid(True, alpha=0.3)
            axes[1, 0].legend()

            # 设置合理的坐标轴范围
            axes[1, 0].set_xlim(0, len(trade_pnls) + 10)
            pnl_min, pnl_max = min(trade_pnls), max(trade_pnls)
            pnl_range = pnl_max - pnl_min
            axes[1, 0].set_ylim(pnl_min - pnl_range * 0.1, pnl_max + pnl_range * 0.1)

            # 添加最终PnL标注
            axes[1, 0].annotate(f'Final: {trade_pnls[-1]:.1f}',
                               xy=(len(trade_pnls), trade_pnls[-1]),
                               xytext=(10, 10), textcoords='offset points',
                               bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
                               arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
        else:
            axes[1, 0].text(0.5, 0.5, 'No Trade PnL Data', ha='center', va='center', transform=axes[1, 0].transAxes)
            axes[1, 0].set_title('Cumulative PnL by Trade Number')
    else:
        axes[1, 0].text(0.5, 0.5, 'No Trade Data', ha='center', va='center', transform=axes[1, 0].transAxes)
        axes[1, 0].set_title('Cumulative PnL by Trade Number')

    # 5. 交易分布分析
    if trades:
        profit_trades = []
        loss_trades = []

        for trade in trades:
            if isinstance(trade, dict) and 'pnl' in trade:
                pnl = trade['pnl']
            elif hasattr(trade, 'pnl'):
                pnl = trade.pnl
            elif hasattr(trade, '__dict__') and 'pnl' in trade.__dict__:
                pnl = trade.__dict__['pnl']
            else:
                pnl = 0

            if pnl > 0:
                profit_trades.append(pnl)
            elif pnl < 0:
                loss_trades.append(pnl)

        if profit_trades or loss_trades:
            all_pnls = profit_trades + loss_trades
            n_bins = min(30, max(10, len(all_pnls) // 5))  # 动态调整bin数量
            axes[1, 1].hist(all_pnls, bins=n_bins, alpha=0.7, color='lightblue', edgecolor='black')
            axes[1, 1].axvline(x=0, color='red', linestyle='--', linewidth=2, label='Break Even')
            axes[1, 1].set_title('Trade PnL Distribution')
            axes[1, 1].set_xlabel('Trade PnL (CNY)')
            axes[1, 1].set_ylabel('Frequency')
            axes[1, 1].grid(True, alpha=0.3)
            axes[1, 1].legend()

            # 添加统计信息
            profit_count = len(profit_trades)
            loss_count = len(loss_trades)
            axes[1, 1].text(0.02, 0.98, f'Profit: {profit_count}\nLoss: {loss_count}',
                           transform=axes[1, 1].transAxes, verticalalignment='top',
                           bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        else:
            axes[1, 1].text(0.5, 0.5, 'No PnL Distribution Data', ha='center', va='center', transform=axes[1, 1].transAxes)
            axes[1, 1].set_title('Trade PnL Distribution')
    else:
        axes[1, 1].text(0.5, 0.5, 'No Trade Data', ha='center', va='center', transform=axes[1, 1].transAxes)
        axes[1, 1].set_title('Trade PnL Distribution')

    # 6. 关键指标汇总
    axes[1, 2].axis('off')
    
    total_return = results.get('total_return', 0) * 100
    sharpe_ratio = results.get('sharpe_ratio', 0)
    calmar_ratio = results.get('calmar_ratio', 0)
    max_drawdown = results.get('max_drawdown', 0) * 100
    total_trades = results.get('total_trades', 0)
    win_rate = results.get('win_rate', 0) * 100
    final_equity = results.get('final_equity', 0)

    # 计算交易统计
    profit_trades = len([t for t in trades if
                        (isinstance(t, dict) and t.get('pnl', 0) > 0) or
                        (hasattr(t, 'pnl') and t.pnl > 0) or
                        (hasattr(t, '__dict__') and t.__dict__.get('pnl', 0) > 0)])
    loss_trades = len([t for t in trades if
                      (isinstance(t, dict) and t.get('pnl', 0) < 0) or
                      (hasattr(t, 'pnl') and t.pnl < 0) or
                      (hasattr(t, '__dict__') and t.__dict__.get('pnl', 0) < 0)])
    
    metrics_text = f"""
Key Performance Metrics

Total Return: {total_return:.2f}%
Sharpe Ratio: {sharpe_ratio:.4f}
Calmar Ratio: {calmar_ratio:.4f}
Max Drawdown: {max_drawdown:.2f}%
Total Trades: {total_trades}
Profit Trades: {profit_trades}
Loss Trades: {loss_trades}
Win Rate: {win_rate:.2f}%
Final Equity: {final_equity:,.0f} CNY

Objective Score: {sharpe_ratio + calmar_ratio:.4f}
"""
    
    axes[1, 2].text(0.1, 0.9, metrics_text, transform=axes[1, 2].transAxes, fontsize=12,
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    axes[1, 2].set_title('Performance Summary')
    
    plt.tight_layout()
    
    # 保存图表
    save_path = "optimization_results/pnl_analysis_best.png"
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"✅ PnL analysis saved: {save_path}")
    
    plt.close()
    
    return results

def save_complete_results(study, backtest_results):
    """保存完整结果"""
    print("💾 保存完整结果...")
    
    # 保存最佳配置
    best_config = {
        'strategy': 'qmt_mm',
        'optimization': {
            'best_score': study.best_value,
            'best_params': study.best_params,
            'total_trials': len(study.trials),
            'completed_trials': len([t for t in study.trials if t.state == optuna.trial.TrialState.COMPLETE])
        },
        'backtest_results': {
            'total_return': backtest_results.get('total_return', 0),
            'sharpe_ratio': backtest_results.get('sharpe_ratio', 0),
            'calmar_ratio': backtest_results.get('calmar_ratio', 0),
            'sortino_ratio': backtest_results.get('sortino_ratio', 0),
            'max_drawdown': backtest_results.get('max_drawdown', 0),
            'total_trades': backtest_results.get('total_trades', 0),
            'win_rate': backtest_results.get('win_rate', 0),
            'final_equity': backtest_results.get('final_equity', 0)
        }
    }
    
    with open('optimization_results/complete_results.json', 'w', encoding='utf-8') as f:
        json.dump(best_config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Complete results saved: optimization_results/complete_results.json")

def main():
    """主函数"""
    print("🎨 生成完整的优化分析图表")
    print("=" * 60)
    
    # 确保目录存在
    os.makedirs('optimization_results', exist_ok=True)
    
    # 加载优化结果
    study = load_optimization_results()
    if study is None:
        return
    
    print(f"📊 优化结果概览:")
    print(f"   总试验数: {len(study.trials)}")
    print(f"   最佳得分: {study.best_value:.6f}")
    print(f"   最佳参数: {len(study.best_params)} 个")
    
    # 生成优化分析图表
    generate_optimization_charts(study)
    
    # 生成PnL曲线图
    backtest_results = generate_pnl_charts(instrument_id='STK_688041')
    
    # 保存完整结果
    if backtest_results:
        save_complete_results(study, backtest_results)
    
    print(f"\n🎉 所有图表生成完成！")
    print(f"📁 保存位置: optimization_results/")
    print(f"   - optimization_analysis.png (优化分析)")
    print(f"   - pnl_analysis_best.png (PnL分析)")
    print(f"   - complete_results.json (完整结果)")

if __name__ == "__main__":
    main()
