from enum import Enum
from typing import Optional
import numpy as np
from .AbstractAkSolver import AbstractAkSolver
from .AkMultiCurveSolver import AkMultiCurveSolver
from .AkRegressionSolver import AkRegressionSolver

"""
A k求解器的工厂
"""
class SolverType(Enum):
    LOG_REGRESSION = 1
    MULTI_CURVE = 2

class AkSolverFactory:
    def __init__(self, solver_type: SolverType):
        """
        @param solver_type 求解器类型
        """
        self.solver_type = solver_type

    def get_solver(self, spread_specification: np.ndarray) -> Optional[AbstractAkSolver]:
        """
        @param spread_specification 用于估计的价差
        @return 具体的A和k估计器
        """
        if self.solver_type == SolverType.MULTI_CURVE:
            return AkMultiCurveSolver(spread_specification)
        elif self.solver_type == SolverType.LOG_REGRESSION:
            return AkRegressionSolver(spread_specification)
        return None
