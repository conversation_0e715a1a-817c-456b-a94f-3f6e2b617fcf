#encoding:gbk
'''

'''
import pandas as pd
import numpy as np
# import talib
import math
from typing import List, Tuple
import datetime
import time
import os
import csv
# from scipy import linregress

class EmpiricalIntensityEstimator:
	"""
	限价单信息的容器
	"""
	class LimitOrderTracker:
		def __init__(self, start_ts: int, order_price: float):
			self.start_ts = start_ts
			self.order_price = order_price

	class Fill:
		def is_order_filled(self, filled_price: float, order_price: float) -> bool:
			raise NotImplementedError()

	class SellFill(Fill):
		def is_order_filled(self, filled_price: float, order_price: float) -> bool:
			return filled_price > order_price

	class BuyFill(Fill):
		def is_order_filled(self, filled_price: float, order_price: float) -> bool:
			return filled_price < order_price

	def __init__(self, spread: float, spread_direction: float, dt: int):
		"""
		@param spread 与中间价的距离，买入限价单使用负号，卖出限价单使用正号
		@param spread_direction -1 表示卖出限价单，1 表示买入限
		"""
		self.spread = spread
		self.dt = dt
		self.initializing = True
		self.last_price = float('nan')
		self.last_limit_order_inserted = 0
		self.live_trackers: List[LimitOrderTracker] = [] # type: ignore
		self.live_trackers_start_time_sum = 0
		self.finished_trackers: List[Tuple[int, int]] = []
		self.finished_trackers_wait_time_sum = 0
		self.fill_comp = self.SellFill() if spread_direction > 0 else self.BuyFill()

	def on_tick(self, ref_price: float, fill_price: float, ts: int, window_start: int):

		if self.initializing:
			self.initializing = False
			self.last_limit_order_inserted = ts - self.dt

		while self.last_limit_order_inserted + self.dt < ts:
			self.last_limit_order_inserted += self.dt
			self.live_trackers.append(
				self.LimitOrderTracker(self.last_limit_order_inserted, self.last_price + self.spread)
			)
			self.live_trackers_start_time_sum += self.last_limit_order_inserted

		if self.last_limit_order_inserted + self.dt == ts:
			self.last_limit_order_inserted = ts
			self.live_trackers.append(self.LimitOrderTracker(ts, ref_price + self.spread))
			self.live_trackers_start_time_sum += ts

		self.last_price = ref_price

		for tracker in self.live_trackers[:]:
			if window_start > tracker.start_ts:
				self.live_trackers.remove(tracker)
				self.live_trackers_start_time_sum -= tracker.start_ts
				continue

			if self.fill_comp.is_order_filled(fill_price, tracker.order_price):
				self.live_trackers.remove(tracker)
				self.live_trackers_start_time_sum -= tracker.start_ts
				duration = ts - tracker.start_ts
				self.finished_trackers.append((tracker.start_ts, duration))
				self.finished_trackers_wait_time_sum += duration

	def estimate_intensity(self, ts: int, window_start: int) -> float:
		
		for tracker in self.finished_trackers[:]:
			if tracker[0] < window_start:
				self.finished_trackers.remove(tracker)
				self.finished_trackers_wait_time_sum -= tracker[1]

		if self.live_trackers and ts != self.live_trackers[-1].start_ts:
			for tracker in self.live_trackers[:]:
				if window_start > tracker.start_ts:
					self.live_trackers.remove(tracker)
					self.live_trackers_start_time_sum -= tracker.start_ts

		if not self.live_trackers:
			return 0.0

		numerator = self.dt * len(self.finished_trackers)  
		denominator = (  
			len(self.live_trackers) * ts  
			- self.live_trackers_start_time_sum  
			+ self.finished_trackers_wait_time_sum  
		)  

		if denominator == 0:  
			return 0.0 
		return max(float(numerator) / denominator, 1e-6)
	
class AbstractAkSolver:
	def __init__(self, spread_specification: np.ndarray):

		self.spread_specification = np.abs(spread_specification)

	def solve_ak(self, intensities: np.ndarray) -> np.ndarray:

		raise NotImplementedError("子类必须实现此方法")

class AkMultiCurveSolver(AbstractAkSolver):
	def __init__(self, spread_specification: np.ndarray):
		"""
		近似求解A k
		"""
		super().__init__(spread_specification)
		n_estimates = len(spread_specification) * (len(spread_specification) - 1) // 2
		self.k_estimates = np.zeros(n_estimates)
		self.a_estimates = np.zeros(n_estimates)

	def solve_ak(self, intensities: np.ndarray) -> np.ndarray:

		est_idx = 0
		with np.errstate(divide='ignore', invalid='ignore'):
			for i in range(len(intensities) - 1):
				for j in range(i + 1, len(intensities)):
					
					self.k_estimates[est_idx] = (
						np.log(intensities[j] / intensities[i]) / 
						(self.spread_specification[i] - self.spread_specification[j])
					)
					self.a_estimates[est_idx] = (
						intensities[i] * np.exp(self.k_estimates[est_idx] * self.spread_specification[i])
					)
					est_idx += 1
		
		return np.array([np.mean(self.a_estimates), np.mean(self.k_estimates)])

class SpreadIntensityCurve: 
	def __init__(self, spread_step: float, n_spreads: int, dt: int):
		# 这里 dt = 3000ms 了 由于 qmt本身的限制
		self.intensity_estimators: List[EmpiricalIntensityEstimator] = []
		spread_specification = np.zeros(n_spreads)
		self.intensity_estimates = np.zeros(n_spreads)
		
		for i in range(n_spreads):
			spread_specification[i] = i * spread_step
			self.intensity_estimators.append(
				EmpiricalIntensityEstimator(spread_specification[i], np.sign(spread_step), dt)
			)
		self.solver = AkMultiCurveSolver(spread_specification)

	def on_tick(self, ref_price: float, fill_price: float, ts: int, window_start: int):

		for estimator in self.intensity_estimators:
			estimator.on_tick(ref_price, fill_price, ts, window_start)

	def estimate_ak(self, ts: int, window_start: int) -> np.ndarray:
		for i, estimator in enumerate(self.intensity_estimators):
			self.intensity_estimates[i] = estimator.estimate_intensity(ts, window_start)
		spread_specification = np.array([estimator.spread for estimator in self.intensity_estimators])
		return self.solver.solve_ak(self.intensity_estimates)

def save_strategy_data(total_vol, mm_avg_price,enter_price):
	try:
		# 准备保存的数据记录
		strategy_record = {
			'timestamp': datetime.datetime.now().isoformat(),
			'date': datetime.datetime.now().strftime('%Y-%m-%d'),
			'time': datetime.datetime.now().strftime('%H:%M:%S'),
			'mm_vol': total_vol,
			'avg_pirce': mm_avg_price,
			'enter_price': enter_price
		}

		df_weights = pd.DataFrame([strategy_record])
		df_weights.to_csv("mm_holding.csv", index=False, encoding='utf-8-sig',mode='w')
		print(f"? 权重已保存到 mm_holding.csv")

	except Exception as e:
		print(f"? 保存策略数据失败: {e}")
		
def load_holding():
	try:
		if os.path.exists("mm_holding.csv"):
			with open("mm_holding.csv", 'r', encoding='utf-8-sig') as f:
				reader = csv.DictReader(f)
				list_of_dicts = list(reader)
				
				return float(list_of_dicts[0]['avg_pirce']), float(list_of_dicts[0]['mm_vol'])
				
		else:
			strategy_record = {
				'timestamp': datetime.datetime.now().isoformat(),
				'date': datetime.datetime.now().strftime('%Y-%m-%d'),
				'time': datetime.datetime.now().strftime('%H:%M:%S'),
				'mm_vol': 0.0,
				'avg_pirce': 0.0,
			}
			df_weights = pd.DataFrame([strategy_record])
			df_weights.to_csv("mm_holding.csv", index=False, encoding='utf-8-sig',mode='w')
			print(f"新建仓位文件 mm_holding.csv")
			return 0,0 
	except Exception as e:
		print(f"? 读取策略数据失败: {e}")
		return 0,0
	
class ASMarket_Maker:
	def __init__(self):
		self.etf_list = []  # 可T+0的etf
		self.account = ''  # 交易账号
		self.account_type = ''  # 账号类型
		self.waiting_list = []  # 未查到委托列表
		self.etf_list = '688041.SH' # 513280
		self.ema_span = 0.005  
		self.ema_spread = -0.004
		self.intensity_nspread = 5
		self.intensity_window = 180 # 估算时间长度 单位秒
		self.sell_enble = False
		self.timestamp_update_price = 0
		self.spread_specification = None
		self.fee = 1/10000
		self.order_update_interval = 10000
		# self.solver = 'linregress'
		self.buy_est = None
		self.sell_est = None
		self.price_step = 0.01 #最小价格差 ETF=0.001 stock = 0.01
		self.trade_etf = '688041.SH'
		self.buy_price = 0
		self.sell_price = 0
		self.exit_price = 0
		self.last_bid_spread = 0  # 上次有效的bid spread
		self.last_ask_spread = 0  # 上次有效的ask spread
		self.tp_spread = 0.54 # 止盈 spread 如果 exit pct = 0.5 第二次止盈是成本价的 2*tp_spread 
		self.reach_tp1 = False # 止盈 加仓后是否有第一次止盈
		self.if_add_vol = False
		self.last_valid_time = 0
		self.enter_lot = 200 #
		self.grid_spread = 0.82
		self.grid_spreadnet_param = [1, 2.5, 5, 7.5]
		self.grid_spreadqty_param = [1, 1, 2, 2]
		self.reenter_price = [0, 0 , 0, 0]
		self.grid_add_layer = 0
		self.enter_price = 0
		self.exit_pct = 0.5
		self.risk_probility = 0.005
		self.timestamp_update_price = 0
		self.pending_sell_orders = {}  # 待成交的卖出订单 {order_type: timestamp}
		self.pending_buy_orders = {}   # 待成交的买入订单 {order_type: timestamp}  
		self.order_timeout = 4000
		self.internal_cost = -1.0
		self.internal_vol = 0
		self.sl_ratio = 10
		self.ask_entropy = 0
		self.bid_entropy = 0
		self.total_value = 0

		# === 新增：entropy比值和EMA相关参数 ===
		self.entropy_ratio = 1.0  # bid_entropy / ask_entropy 比值
		self.entropy_ratio_ema = 1.0  # entropy比值的EMA
		self.bid_entropy_ema = 0.0  # bid entropy的EMA
		self.ask_entropy_ema = 0.0  # ask entropy的EMA

		# EMA平滑参数
		self.entropy_ema_alpha = 0.3  # EMA平滑系数
		self.ratio_ema_alpha = 0.2    # 比值EMA平滑系数

		# 基于entropy比值的交易信号阈值
		self.entropy_ratio_buy_threshold = 1.5   # 当ask/bid entropy比值 > 1.2时，倾向买入
		self.entropy_ratio_sell_threshold = 0.5  # 当ask/bid entropy比值 < 0.8时，倾向卖出

		# 基于entropy EMA的价差调整参数
		self.entropy_ema_spread_factor = 1.1  # EMA变化对价差的影响系数
		self.buy_a = 0
		self.buy_k = 0
		self.timestamp_hold_pos = 0
		self.max_holdtime = 1800*1000
		self.extra_vol = 0
		self.last_askp1 = 0
		self.last_bidp1 = 0
		self.init_timestamp = 0
		self.use_limit_momentum = False
		self.mom_order_timestamp = -1
		self.limit_order_deal = False
		self.tp_timestamp = 0
# 强度价差预测
def get_intensity(target_spread: float, a: float, k: float) -> float:
	return a * math.exp(-k * target_spread)

def get_spread(target_intensity: float, a: float, k: float) -> float:
	return -(math.log(target_intensity / a)) / k

def dynamic_mid(ask, bid, ask_qty = 0.001, bid_qty = 0.001):
	try:
		imbalance = max(ask_qty, bid_qty) / min(ask_qty, bid_qty)
	except:
		print("涨停or跌停")
		return max(ask, bid)
	if imbalance > 2.0:
		return (ask * bid_qty + bid * ask_qty) / (bid_qty + ask_qty)
	else:
		return (ask + bid) / 2
	
def factor_entropy(tick, type, level = 5):
	total_vol = 0
	for i in range(5):
		if type =='bid' or type =='buy':
			total_vol += tick['bidVol'][i]
		elif type =='ask' or type =='sell':
			total_vol += tick['askVol'][i]
		else:
			return -1
	entropy = 0
	for i in range(level):
		if type =='bid' or type =='buy':
			entropy -= (tick['bidVol'][i] / total_vol) * math.log(tick['bidVol'][i] / total_vol)
		elif type =='ask' or type =='sell':
			entropy -= (tick['askVol'][i] / total_vol) * math.log(tick['askVol'][i] / total_vol)
	return entropy

def update_entropy_metrics(bid_entropy, ask_entropy):
	"""
	更新entropy相关指标：比值和EMA
	"""
	# 计算ask/bid entropy比值（修正方向）
	# 比值 > 1: ask分散/bid集中 = 买盘强势
	# 比值 < 1: ask集中/bid分散 = 卖盘强势
	if bid_entropy > 0.001:
		current_entropy_ratio = ask_entropy / bid_entropy
	else:
		current_entropy_ratio = 1.0  


	if MM.bid_entropy_ema == 0:
		MM.bid_entropy_ema = bid_entropy
		MM.ask_entropy_ema = ask_entropy
		MM.entropy_ratio_ema = current_entropy_ratio
	else:
		MM.bid_entropy_ema = MM.bid_entropy_ema * (1 - MM.entropy_ema_alpha) + bid_entropy * MM.entropy_ema_alpha
		MM.ask_entropy_ema = MM.ask_entropy_ema * (1 - MM.entropy_ema_alpha) + ask_entropy * MM.entropy_ema_alpha
		MM.entropy_ratio_ema = MM.entropy_ratio_ema * (1 - MM.ratio_ema_alpha) + current_entropy_ratio * MM.ratio_ema_alpha

	# 更新当前比值
	MM.entropy_ratio = current_entropy_ratio

	return current_entropy_ratio

def get_entropy_trading_signal(bid_entropy,ask_entropy):
	"""
	基于entropy比值和EMA生成交易信号
	返回: (signal, strength)
	signal: 'buy', 'sell', 'hold'
	strength: 信号强度 0-1
	"""
	if MM.bid_entropy_ema == 0 or MM.ask_entropy_ema ==0:
		return 'hold', 0.0	
	
	if MM.entropy_ratio_ema == 0:
		return 'hold', 0.0

	# 基于当前比值的信号
	if ask_entropy/bid_entropy > MM.entropy_ratio_buy_threshold:
		# ask entropy高，卖盘分散，买盘集中，倾向买入
		signal = 'buy'
		strength = min((ask_entropy/bid_entropy- 1.0) / 0.5, 1.0)  # 标准化到0-1
	elif ask_entropy/bid_entropy < MM.entropy_ratio_sell_threshold:
		# bid entropy高，买盘分散，卖盘集中，倾向卖出
		signal = 'sell'
		strength = min((1.0 - ask_entropy/bid_entropy) / 0.5, 1.0)  # 标准化到0-1
	else:
		signal = 'hold'
		strength = 0.0

	# 基于entropy_ratio突破entropy_ratio_ema的增强逻辑
	ema_trend_factor = 1.0
	if bid_entropy / MM.bid_entropy_ema > MM.entropy_ema_spread_factor:
		if signal == 'hold':
			signal = 'sell'
			strength = 0.2 
		if signal == 'buy':
			signal = 'hold'
			strength = 0.0# 最多增强60%
		if signal == 'sell':
			strength = min(strength * 1.2, 1.0)
	elif ask_entropy / MM.ask_entropy_ema > MM.entropy_ema_spread_factor:
		if signal == 'hold':
			signal = 'buy'
			strength = 0.2  # 最多增强60%

	final_strength = min(strength * ema_trend_factor, 1.0)

	return signal, final_strength


def check_and_clean_pending_orders(current_time):
	"""
	检查并清理超时的待处理订单
	"""
	# 清理超时的卖出订单
	for order_type in list(MM.pending_sell_orders.keys()):
		if current_time - MM.pending_sell_orders[order_type] > MM.order_timeout:
			print(f"清理超时的卖出订单: {order_type}")
			del MM.pending_sell_orders[order_type]
	
	# 清理超时的买入订单
	for order_type in list(MM.pending_buy_orders.keys()):
		if current_time - MM.pending_buy_orders[order_type] > MM.order_timeout:
			print(f"清理超时的买入订单: {order_type}")
			del MM.pending_buy_orders[order_type]

def is_order_pending(order_type, order_direction='sell'):
	"""
	检查指定类型的订单是否仍在待处理状态
	"""
	if order_direction == 'sell':
		return order_type in MM.pending_sell_orders
	else:
		return order_type in MM.pending_buy_orders

def record_pending_order(order_type, current_time, order_direction='sell'):
	"""
	记录待处理的订单
	"""
	if order_direction == 'sell':
		MM.pending_sell_orders[order_type] = current_time
		print(f"记录待处理卖出订单: {order_type}")
	else:
		MM.pending_buy_orders[order_type] = current_time
		print(f"记录待处理买入订单: {order_type}")

def clear_pending_order_by_reason(reason):
	"""
	根据交易原因清理对应的待处理订单
	"""
	# 映射交易原因到订单类型
	reason_to_order_type = {
		'take_profit_1': 'take_profit_1',
		'take_profit_2_full': 'take_profit_2_full', 
		'simple_profit': 'simple_profit',
		'stop_loss':'stop_loss'
	}
	
	order_type = reason_to_order_type.get(reason)
	if order_type and order_type in MM.pending_sell_orders:
		del MM.pending_sell_orders[order_type]
		print(f"清理已成交的订单: {order_type}")

def order_callback(order_info):
	"""
	订单回调函数，用于处理订单状态变化
	在QMT中，当订单状态发生变化时会调用此函数
	"""
	try:
		# 检查订单是否已成交
		if hasattr(order_info, 'm_nOrderStatus') and order_info.m_nOrderStatus == 48:  # 48表示全部成交
			# 根据订单信息推断交易类型并清理对应的待处理订单
			order_remark = getattr(order_info, 'm_strOrderRemark', '')
			
			# 这里可以根据订单备注或其他字段来判断订单类型
			# 由于QMT的限制，我们可能需要根据实际的订单字段来调整
			if 'sell' in order_remark.lower():
				# 简化处理：如果是卖出订单，清理所有待处理的卖出订单
				# 在实际使用中，应该根据更具体的信息来匹配订单类型
				MM.pending_sell_orders.clear()
				print("检测到卖出订单成交，清理所有待处理卖出订单")
			elif 'buy' in order_remark.lower():
				# 如果是买入订单，清理所有待处理的买入订单
				MM.pending_buy_orders.clear()
				print("检测到买入订单成交，清理所有待处理买入订单")
		
	except Exception as e:
		print(f"处理订单回调时出错: {e}")
	
MM = ASMarket_Maker()

def max_vol_spread(tick):
	max_value = max(tick['bidVol'])
	max_index = tick['bidVol'].index(max_value)
	return tick['lastPrice'] - tick['bidPrice'][max_index]

def shortly_momentum(ap_t1, bp_t1, ap_n, bp_n): #t1是上一个时刻
	signal = 'hold'
	momentum = 0.0
	if bp_t1 > ap_n:
		return 'sell', 0.75
	elif ap_t1 < bp_n:
		return 'buy', 0.75
	elif ap_t1 < ap_n and bp_t1 < bp_n:
		return 'buy', 0.5
	elif ap_t1 > ap_t1 and bp_t1 > bp_n:
		return 'sell', 0.5
	
	return  signal, momentum
	
def init(ContextInfo):
	MM.etf_list = [
	'513120.SH', '159776.SZ', '159615.SZ', '513200.SH', '513060.SH', '159892.SZ', '513280.SH', '159718.SZ',
	'513360.SH', '513700.SH', '513020.SH', '501021.SH', '513980.SH', '513860.SH', '513090.SH', '501311.SH',
	'513530.SH', '513320.SH', '159750.SZ', '513150.SH', '159788.SZ', '159747.SZ', '159960.SZ', '159954.SZ',
	'513680.SH', '159792.SZ', '159850.SZ', '159712.SZ', '159823.SZ', '160416.SH', '513770.SH', '513160.SH',
	'513010.SH', '510900.SH', '161128.SH', '159726.SZ', '159822.SZ', '501025.SH', '513590.SH', '159751.SZ',
	'159607.SZ', '513890.SH', '513990.SH', '513080.SH', '164705.SH', '159866.SZ', '513260.SH', '164824.SH',
	'513580.SH', '160322.SH', '518860.SH', '513690.SH', '511380.SH', '511260.SH', '513300.SH', '511060.SH',
	'511180.SH', '511970.SH', '511020.SH', '511270.SH', '511950.SH', '511220.SH', '511910.SH', '159832.SZ',
	'511600.SH', '511620.SH', '511010.SH', '159816.SZ', '511930.SH', '511670.SH', '159972.SZ', '511880.SH',
	'511920.SH', '511660.SH', '511820.SH', '511800.SH', '511830.SH', '511700.SH', '511900.SH', '511850.SH',
	'511990.SH', '511690.SH', '511360.SH', '511650.SH', '161116.SH', '160717.SH', '159741.SZ', '159740.SZ',
	'159711.SZ', '159005.SZ', '513900.SH', '513660.SH', '513380.SH', '513180.SH', '501302.SH', '159001.SZ']
	MM.account = '********'
	MM.account_type = 'STOCK'
	print(f'ETF T+0做市初始化中，可用标的有 {len(MM.etf_list)}只ETF')
	MM.buy_est = SpreadIntensityCurve(spread_step=-MM.price_step, n_spreads= MM.intensity_nspread, dt = 1500)
	MM.sell_est = SpreadIntensityCurve(spread_step=MM.price_step, n_spreads= MM.intensity_nspread, dt = 1500)
	MM.init_timestamp = datetime.datetime.now().timestamp()
	return MM
def handlebar(ContextInfo):
	# === 新增：订单状态管理 ===
	now = datetime.datetime.now()
	high_vol = True
	current_time = get_market_time("SH")
	unix_time = now.timestamp() * 1000
	if now.timestamp() - MM.init_timestamp < 3:
		print("初始化程序中...")
		return

	# 清理超时的订单
	check_and_clean_pending_orders(unix_time)
	etf_holdings = []
	position_info = get_trade_detail_data(MM.account, 'stock','position')
	last_order_number = None
	if MM.use_limit_momentum and not MM.limit_order_deal:
		last_order_number = get_last_order_id(MM.account, 'stock','order')
		last_order_info = get_value_by_order_id(last_order_number, MM.account, 'stock','order')
		if last_order_info.m_strInstrumentID != MM.trade_etf[:6] or MM.enter_price!=last_order_info.m_dLimitPrice:
			print("插入单可能已成交")
			MM.limit_order_deal = True
		else:
			if unix_time - MM.mom_order_timestamp >= 10000:
				cancel(last_order_number, MM.account, 'stock',ContextInfo)
				print("插入单超时未成交，取消订单")
				
				MM.use_limit_momentum = False
				save_strategy_data(0, 0, 0)
			else:
				print("插入单未超时，继续等待")
				return
	# for position in position_info:
	# 	stock_code = position.m_strInstrumentID
	# 	# 检查持仓代码是否在ETF列表中（带交易所后缀）
	# 	if stock_code == MM.trade_etf[:6]:
	# 		# 记录持仓详细信息
	# 		holding_data = {
	# 			'code': stock_code,
	# 			'volume': position.m_nVolume,      # 持仓数量
	# 			# 'available': position.m_nCanUseVolume,  # 可用数量
	# 			'cost': position.m_dOpenPrice,          # 成本价
	# 		}
	tick = ContextInfo.get_full_tick([MM.trade_etf])
	# last_price = tick[MM.trade_etf]['lastPrice']
	tick = tick[MM.trade_etf]	# 		etf_holdings.append(holding_data)
	MM.internal_cost, MM.internal_vol = load_holding()

	MM.sell_enble = True if MM.internal_vol > 0 else False
	MM.if_add_vol = True if MM.internal_vol > MM.enter_lot else False
	if MM.internal_cost > 0 and not MM.use_limit_momentum:
		MM.exit_price = MM.internal_cost + MM.tp_spread 
	elif MM.internal_cost > 0 and MM.use_limit_momentum:
		MM.exit_price = MM.internal_cost + max(0.3, tick['askPrice'][0] - tick['bidPrice'][0])
	else:
		MM.exit_price = 0

	if MM.internal_vol > 0:
		print("当前MM持仓: ",MM.internal_vol,"手", " 成本价: ",MM.internal_cost, " 止盈价: ",MM.exit_price)
		print("加仓价格:", MM.reenter_price, " 当前加仓层级:", MM.grid_add_layer)
		print("当前ask价格:", tick['askPrice'][0], " 下一层加仓价格:", MM.reenter_price[MM.grid_add_layer] if MM.grid_add_layer < len(MM.reenter_price) else "已达最大层级")
	# holdtime = unix_time - MM.timestamp_hold_pos if MM.timestamp_hold_pos > 0 else 0
	mid_price = dynamic_mid(tick['askPrice'][0], tick['bidPrice'][0], tick['askVol'][0], tick['bidVol'][0])

	MM.sell_est.on_tick(mid_price, tick['askPrice'][0], unix_time, unix_time - MM.intensity_window * 1000)
	MM.buy_est.on_tick(mid_price, tick['bidPrice'][0], unix_time, unix_time - MM.intensity_window * 1000)

	buy_ak = MM.buy_est.estimate_ak(unix_time, unix_time - MM.intensity_window * 1000)
	sell_ak = MM.sell_est.estimate_ak(unix_time, unix_time - MM.intensity_window * 1000)
	#print(buy_ak,sell_ak)
	# if not np.any(np.isnan(buy_ak)):  
    ## 明日检查 bid ask的问题
	bid_spread = 0.000
	ask_spread = 0.000
	if not np.any(np.isnan(buy_ak)):
		if  buy_ak[0] * buy_ak[1] != 0:
			filtered_bid_intensity = MM.buy_est.intensity_estimates[MM.buy_est.intensity_estimates > 1e-6]
			bid_spread = get_spread(MM.risk_probility, buy_ak[0], buy_ak[1])
			bid_spread = max(bid_spread, 0.01)
			print(buy_ak,sell_ak,' ',filtered_bid_intensity)
			if MM.buy_a <= 0 or MM.buy_k <=0:
				MM.buy_a = buy_ak[0]
				MM.buy_k = buy_ak[1]
			else:
				MM.buy_a = MM.buy_a * 0.1 + buy_ak[0] * 0.9
				MM.buy_k = MM.buy_k * 0.1 + buy_ak[1] * 0.9
	else:
		if MM.buy_a * MM.buy_a !=0:
			bid_spread = get_spread(MM.risk_probility, MM.buy_a, MM.buy_k)
			bid_spread = max(bid_spread, 0.022)
			print(' old ak = ',MM.buy_a,MM.buy_k)
			
	if not np.any(np.isnan(sell_ak)):
		if  sell_ak[0] * sell_ak[1] != 0:
			filtered_ask_intensity = MM.sell_est.intensity_estimates[MM.sell_est.intensity_estimates > 1e-6]
			if len(filtered_ask_intensity) and filtered_ask_intensity[-1] * sell_ak[0] > 0:
				if len(filtered_ask_intensity) < 4:
					ask_spread = get_spread(0.01, sell_ak[0], sell_ak[1]) 
				else:
					ask_spread = get_spread(0.01, sell_ak[0], sell_ak[1])
	spread_lifetime = 9000
	maxvol_bid_spread = max_vol_spread(tick)
	if unix_time - MM.last_valid_time > spread_lifetime and bid_spread <= 0:    
		if bid_spread > maxvol_bid_spread:
			bid_spread = maxvol_bid_spread


	ask_entropy = factor_entropy(tick, 'ask')
	bid_entropy = factor_entropy(tick, 'bid')
	markttype , strength = get_entropy_trading_signal(bid_entropy, ask_entropy)
	entropy_rate =update_entropy_metrics(bid_entropy, ask_entropy)
	print("ask entropy",ask_entropy, " bid entropy",bid_entropy," entropy ratio:", entropy_rate)
	
	print("market type:",markttype, " strength:", strength)
	# if not MM.bid_entropy == 0:
	# 	if bid_entropy- MM.bid_entropy > MM.bid_entropy_threshold:
	# 		bid_spread += 0.001
	# else:
	# 	MM.bid_entropy = bid_entropy
	# if not MM.ask_entropy == 0:
	# 	if ask_entropy- MM.ask_entropy > MM.ask_entropy_threshold_rate:
	# 		bid_spread = max(bid_spread - 0.001, 0.0015)
	# else:
	# 	MM.ask_entropy = ask_entropy
	# 
	#
	# 需要增加一个 向选择的 t-1时刻的 吃盘口动能的
	direction, scores = shortly_momentum(MM.last_askp1, MM.last_bidp1,tick['askPrice'][0], tick['bidPrice'][0])
	print('短期动能: ',direction, " 强度: ", scores)
	if unix_time - MM.timestamp_update_price > MM.order_update_interval * 1000 or \
			markttype != "hold" or direction != "hold":
		if not np.any(np.isnan(buy_ak)):
			if  buy_ak[0] * buy_ak[1] != 0:
				if markttype == "sell":
					bid_spread = get_spread(MM.risk_probility / 2, buy_ak[0], buy_ak[1])
				elif markttype == "buy":
					bid_spread = get_spread(MM.risk_probility * 2, buy_ak[0], buy_ak[1])
		else:
			return
		
		if direction == "sell":
			bid_spread *= 1.2
		if markttype== "sell":
			bid_spread *= 1.1
		MM.buy_price = round((mid_price - bid_spread)*100)/100
		if MM.buy_price > tick['bidPrice'][0] and direction == "sell":
			MM.buy_price = round((tick['bidPrice'][0]- bid_spread)*100)/100
		# MM.sell_price = math.floor((last_price + MM.last_ask_spread)*100)/100
		MM.timestamp_update_price = unix_time
		print("中间价 ",mid_price," 买1价", tick['bidPrice'][0])
		print('价格更新：')
		print(f'bid挂单价格: {MM.buy_price:.3f}  bid spread', bid_spread, 'max bidvol spread', maxvol_bid_spread)
		# print(f'ask挂单价格: {MM.sell_price:.3f}')
	# MM.ask_entropy = MM.ask_entropy * 0.5 + 0.5 * ask_entropy
	# MM.bid_entropy = MM.bid_entropy * 0.5 + 0.5 * bid_entropy
	# 开始 交易部分
	# sl first
	MM.last_bid_spread = bid_spread
	MM.last_ask_spread = ask_spread
	MM.last_askp1 = tick['askPrice'][0]
	MM.last_bidp1 = tick['bidPrice'][0]
	
	if unix_time - MM.tp_timestamp < 120 * 100:
		print("止盈cd中")
		return 

	if MM.internal_vol <=0:
		# 增加一个 盘口动能的 插单
		if direction == "buy" and scores >= 0.5 and (tick['askPrice'][0] - tick['bidPrice'][0]> 0.5) and not MM.use_limit_momentum:
			# if is_order_pending('initial_buy', 'buy'):
			# 	print("开仓买入订单已发送，等待成交...")
			# 	return
			record_pending_order('initial_buy', unix_time, 'buy')
			passorder(23, 
						1101, 
						MM.account, 
						MM.trade_etf,
						11, 
						tick['bidPrice'][0] + 0.03, 
						MM.enter_lot, 
						'ETF T+0', 
						1, 
						'buy', 
						ContextInfo)
			MM.enter_price = tick['bidPrice'][0] + 0.03
			MM.exit_price = MM.enter_price + max(0.3, tick['askPrice'][0] - tick['bidPrice'][0])
			save_strategy_data(MM.enter_lot, MM.enter_price, MM.enter_price)
			MM.use_limit_momentum = True
			MM.timestamp_hold_pos = unix_time
			MM.mom_order_timestamp = unix_time
			print("操作: 买入标的 ",MM.enter_lot,"手",' 当前仓位均价: ',MM.enter_lot)
			MM.grid_add_layer += 1
			save_strategy_data(MM.enter_lot, MM.enter_price, MM.enter_price)
			for i in range(4):
				MM.reenter_price[i] = MM.enter_price - MM.grid_spread * MM.grid_spreadnet_param[i]
			return
			
									 
		if tick['askPrice'][0]<= MM.buy_price:
			# === 新增：检查是否已有待处理的开仓买入订单 ===
			if is_order_pending('initial_buy', 'buy'):
				print("开仓买入订单已发送，等待成交...")
				return
			
			# 记录待处理订单
			record_pending_order('initial_buy', unix_time, 'buy')
			
			passorder(23, 
						1101, 
						MM.account, 
						MM.trade_etf,
						6, 
						-1, 
						MM.enter_lot, 
						'ETF T+0', 
						1, 
						'buy', 
						ContextInfo)
			print("操作: 买入标的 ",MM.enter_lot,"手",' 当前仓位均价: ',MM.internal_cost)
			MM.enter_price = tick['askPrice'][0]
			save_strategy_data(MM.enter_lot, MM.enter_price, MM.enter_price)
			MM.exit_price = MM.enter_price + MM.tp_spread 
			MM.timestamp_hold_pos = unix_time
			MM.grid_add_layer += 1
			for i in range(4):
				MM.reenter_price[i] = MM.enter_price - MM.grid_spread * MM.grid_spreadnet_param[i]
			return
	else:
		if tick['bidPrice'][0]>= MM.exit_price and MM.exit_price > 0:
			if is_order_pending('simple_profit', 'sell'):
				print("简单止盈订单已发送，等待成交...")
				return
			
			# 记录待处理订单
			record_pending_order('simple_profit', unix_time, 'sell')
			MM.tp_timestamp = unix_time
			passorder(24, 
						1101, 
						MM.account, 
						MM.trade_etf,
						6, 
						-1, 
						MM.internal_vol, 
						'ETF T+0', 
						1, 
						'sell', 
						ContextInfo)
			MM.if_add_vol = False
			print("获利 操作: 卖出标的 ",MM.internal_vol,"份")
			MM.sell_enble = False
			MM.grid_add_layer = 0
			MM.internal_cost = -1.0
			MM.enter_price = 0
			MM.reenter_price = [0, 0, 0, 0]
			MM.use_limit_momentum = False
			save_strategy_data(0, 0, 0)
			return
		if tick['askPrice'][0]<= MM.reenter_price[MM.grid_add_layer]:
			if is_order_pending('grid_buy_1', 'buy'):
				print("加仓买入订单已发送，等待成交...")
				return
			
			# 记录待处理订单
			reenter_lot = MM.enter_lot * MM.grid_spreadqty_param[MM.grid_add_layer]
			record_pending_order('grid_buy_1', unix_time, 'buy')				
			passorder(23, 
						1101, 
						MM.account, 
						MM.trade_etf,
						6, 
						-1, 
						reenter_lot, 
						'ETF T+0', 
						1, 
						'buy add', 
						ContextInfo)
			MM.reach_tp1 = False
			MM.internal_cost = (MM.internal_cost * MM.internal_vol + reenter_lot * tick['askPrice'][0]) / (MM.internal_vol + reenter_lot)
			save_strategy_data(reenter_lot + MM.internal_vol, MM.internal_cost, MM.enter_price)
			MM.grid_add_layer += 1
			MM.exit_price = MM.internal_cost + max(MM.tp_spread, 0.002)
			print("操作: 加仓标的 ",MM.enter_lot,"手",' 当前仓位均价: ',MM.internal_cost)
			return
			