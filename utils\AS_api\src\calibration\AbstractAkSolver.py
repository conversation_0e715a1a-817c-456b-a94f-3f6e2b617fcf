from abc import ABC, abstractmethod
import numpy as np

"""
A和k的抽象求解器
"""
class AbstractAkSolver(ABC):
    def __init__(self, spread_specification: np.ndarray):
        """
        @param spread_specification 价差数组（价差-强度曲线的X轴）
        """
        self.spread_specification = np.abs(spread_specification)

    @abstractmethod
    def solve_ak(self, intensities: np.ndarray) -> np.ndarray:
        """
        @param intensities 强度数组（价差-强度曲线的Y轴）
        @return 实现的方法应返回包含估计的A和k的数组 [A, k]
        """
        pass
