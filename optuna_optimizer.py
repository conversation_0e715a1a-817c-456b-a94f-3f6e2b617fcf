#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门的Optuna优化器 - 只负责参数优化
"""

import optuna
import pandas as pd
from typing import Dict, Any, Callable
from tqdm import tqdm
import importlib
import os

from strategy_base import strategy_manager
from backtest_engine import BacktestEngine, BacktestConfig, load_data


class OptunaOptimizer:
    """专门的Optuna优化器"""
    
    def __init__(self, strategy_name: str, backtest_config: BacktestConfig):
        self.strategy_name = strategy_name
        self.backtest_config = backtest_config
        self.train_data = None
        self.test_data = None
        
        # 确保策略已加载
        self._ensure_strategy_loaded()
    
    def _ensure_strategy_loaded(self):
        """确保策略模块已加载"""
        if self.strategy_name not in strategy_manager.list_strategies():
            try:
                module_name = f"strategy_{self.strategy_name}"
                importlib.import_module(module_name)
                print(f"✅ 策略模块 {module_name} 加载成功")
            except ImportError as e:
                raise ImportError(f"❌ 策略模块 strategy_{self.strategy_name} 加载失败: {e}")
    
    def prepare_data(self, data: pd.DataFrame, train_ratio: float = 0.7):
        """准备训练和测试数据"""
        split_idx = int(len(data) * train_ratio)
        self.train_data = data.iloc[:split_idx].copy()
        self.test_data = data.iloc[split_idx:].copy()
        
        print(f"📊 数据分割:")
        print(f"  训练集: {len(self.train_data)} 行")
        print(f"  测试集: {len(self.test_data)} 行")
    
    def objective_function(self, trial: optuna.Trial) -> float:
        """Optuna目标函数 - 核心优化逻辑"""
        # 创建策略实例
        strategy = strategy_manager.create_strategy(self.strategy_name, {})
        
        # 获取优化参数
        optimization_params = strategy.get_optimization_params()
        
        # 生成试验参数
        trial_params = {}
        for param_name, (min_val, max_val) in optimization_params.items():
            if isinstance(min_val, int) and isinstance(max_val, int):
                trial_params[param_name] = trial.suggest_int(param_name, min_val, max_val)
            else:
                trial_params[param_name] = trial.suggest_float(param_name, min_val, max_val)
        
        # 更新策略参数
        strategy.update_params(trial_params)
        
        # 创建回测引擎并运行
        engine = BacktestEngine(self.backtest_config, strategy)
        results = engine.run_backtest(self.train_data)
        
        # 返回目标值（这里使用夏普比率）
        return results['sharpe_ratio']+ results['calmar_ratio']
    
    def optimize(self, 
                n_trials: int = 100, 
                study_name: str = None,
                objective_metric: str = 'sharpe_ratio',
                direction: str = 'maximize') -> Dict[str, Any]:
        """执行优化"""
        if self.train_data is None:
            raise ValueError("请先调用 prepare_data() 准备数据")
        
        # 创建study
        if study_name is None:
            study_name = f"{self.strategy_name}_optimization"

        # 确保optuna_studies目录存在
        os.makedirs("optuna_studies", exist_ok=True)

        study = optuna.create_study(
            direction=direction,
            study_name=study_name,
            storage=f'sqlite:///optuna_studies/{study_name}.db',
            load_if_exists=True
        )
        
        print(f"🚀 开始优化策略: {self.strategy_name}")
        print(f"📊 试验次数: {n_trials}")
        print(f"🎯 目标指标: {objective_metric}")
        print(f"📈 优化方向: {direction}")
        
        # 执行优化
        study.optimize(self.objective_function, n_trials=n_trials, show_progress_bar=True)
        
        # 获取最佳参数
        best_params = study.best_params
        best_value = study.best_value
        
        print(f"🏆 优化完成!")
        print(f"📈 最佳目标值: {best_value:.6f}")
        print(f"🔧 最佳参数: {best_params}")
        
        # 在测试集上验证
        test_results = self.validate_on_test_set(best_params)
        train_results = self.get_train_results(best_params)
        
        return {
            'best_params': best_params,
            'best_value': best_value,
            'train_results': train_results,
            'test_results': test_results,
            'study': study,
            'optimization_history': self._get_optimization_history(study)
        }
    
    def get_train_results(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """获取训练集结果"""
        strategy = strategy_manager.create_strategy(self.strategy_name, {})
        strategy.update_params(params)
        engine = BacktestEngine(self.backtest_config, strategy)
        return engine.run_backtest(self.train_data)
    
    def validate_on_test_set(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """在测试集上验证"""
        if self.test_data is None:
            return {}
        
        print(f"🔍 在测试集上验证...")
        
        strategy = strategy_manager.create_strategy(self.strategy_name, {})
        strategy.update_params(params)
        engine = BacktestEngine(self.backtest_config, strategy)
        results = engine.run_backtest(self.test_data)
        
        print(f"📊 测试集结果:")
        print(f"  总收益率: {results['total_return']:.4f} ({results['total_return']*100:.2f}%)")
        print(f"  夏普比率: {results['sharpe_ratio']:.4f}")
        print(f"  最大回撤: {results['max_drawdown']:.4f} ({results['max_drawdown']*100:.2f}%)")
        print(f"  总交易次数: {results['total_trades']}")
        print(f"  胜率: {results['win_rate']:.4f} ({results['win_rate']*100:.2f}%)")
        print(f"  盈亏比: {results['profit_loss_ratio']:.4f}")
        
        return results
    
    def _get_optimization_history(self, study: optuna.Study) -> Dict[str, Any]:
        """获取优化历史"""
        trials = study.trials
        
        history = {
            'trial_numbers': [t.number for t in trials],
            'values': [t.value for t in trials if t.value is not None],
            'best_values': [],
            'params_history': [t.params for t in trials]
        }
        
        # 计算最佳值历史
        best_so_far = float('-inf') if study.direction == optuna.study.StudyDirection.MAXIMIZE else float('inf')
        for trial in trials:
            if trial.value is not None:
                if study.direction == optuna.study.StudyDirection.MAXIMIZE:
                    best_so_far = max(best_so_far, trial.value)
                else:
                    best_so_far = min(best_so_far, trial.value)
            history['best_values'].append(best_so_far)
        
        return history
    
    def continue_optimization(self, 
                            study_name: str, 
                            additional_trials: int = 50) -> Dict[str, Any]:
        """继续现有的优化"""
        print(f"🔄 继续优化现有研究: {study_name}")
        
        # 加载现有study
        study = optuna.load_study(
            study_name=study_name,
            storage=f'sqlite:///optuna_studies/{study_name}.db'
        )
        
        print(f"📊 已完成试验数: {len(study.trials)}")
        print(f"🏆 当前最佳值: {study.best_value:.6f}")
        print(f"🔢 计划增加试验数: {additional_trials}")
        
        # 继续优化
        study.optimize(self.objective_function, n_trials=additional_trials, show_progress_bar=True)
        
        # 返回结果
        best_params = study.best_params
        best_value = study.best_value
        
        test_results = self.validate_on_test_set(best_params)
        train_results = self.get_train_results(best_params)
        
        return {
            'best_params': best_params,
            'best_value': best_value,
            'train_results': train_results,
            'test_results': test_results,
            'study': study,
            'optimization_history': self._get_optimization_history(study)
        }


def run_optimization(strategy_name: str,
                    data_dir: str = "backtest_data",
                    symbol: str = None,
                    n_trials: int = 100,
                    train_ratio: float = 0.7,
                    backtest_config: BacktestConfig = None) -> Dict[str, Any]:
    """运行优化的便捷函数"""

    # 使用默认配置
    if backtest_config is None:
        backtest_config = BacktestConfig()

    # 加载数据（支持新的数据结构，只使用snapshot数据）
    data = load_data(data_dir, symbol, snapshot_only=True)

    # 创建优化器
    optimizer = OptunaOptimizer(strategy_name, backtest_config)
    optimizer.prepare_data(data, train_ratio)

    # 执行优化
    results = optimizer.optimize(n_trials)

    return results


def continue_optimization(strategy_name: str,
                         study_name: str,
                         data_dir: str = "backtest_data",
                         symbol: str = None,
                         additional_trials: int = 50,
                         train_ratio: float = 0.7,
                         backtest_config: BacktestConfig = None) -> Dict[str, Any]:
    """继续现有优化的便捷函数"""

    # 使用默认配置
    if backtest_config is None:
        backtest_config = BacktestConfig()

    # 加载数据（支持新的数据结构，只使用snapshot数据）
    data = load_data(data_dir, symbol, snapshot_only=True)
    
    # 创建优化器
    optimizer = OptunaOptimizer(strategy_name, backtest_config)
    optimizer.prepare_data(data, train_ratio)
    
    # 继续优化
    results = optimizer.continue_optimization(study_name, additional_trials)
    
    return results
