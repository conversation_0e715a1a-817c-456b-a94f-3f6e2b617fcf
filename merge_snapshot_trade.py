import os
import pandas as pd
import numpy as np
from typing import List, Optional
from datetime import datetime as _dt
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
from functools import partial


def _read_csv_auto(path):
    for enc in ("gbk", "utf-8-sig", "utf-8", "latin1"):
        try:
            return pd.read_csv(path, encoding=enc, low_memory=False)
        except UnicodeDecodeError:
            continue
    raise


def _load_snapshot(snapshot_file: str) -> pd.DataFrame:
    """Load snapshot CSV produced by QMT.

    Parameters
    ----------
    snapshot_file : str
        Path to the snapshot csv (located in backtest_data/snapshot/).

    Returns
    -------
    pd.DataFrame
        Normalised snapshot dataframe with (at minimum) the following columns::

            timestamp      : int   - millisecond unix epoch
            event_type     : str   - literal 'snapshot'
            last_price     : float
            bid_price      : float
            ask_price      : float
            bid_vol        : float
            ask_vol        : float
            last_volume    : float
    """
    # Many QMT csv files are encoded in GBK.
    df = _read_csv_auto(snapshot_file)

    # Build unix-epoch millisecond timestamp.
    if "自然日" not in df.columns or "时间" not in df.columns:
        raise ValueError(f"列 '自然日' 或 '时间' 在文件 {snapshot_file} 中不存在，无法解析时间")
    df["datetime"] = pd.to_datetime(df["自然日"].astype(str) + " " + df["时间"].astype(str))

    # 仅保留 09:30:00 之后的行情
    _trading_start = _dt.strptime("09:30:00", "%H:%M:%S").time()
    df = df[df["datetime"].dt.time >= _trading_start].copy()

    # 修复时间戳转换 - 使用正确的毫秒时间戳（避免int32溢出）
    df["timestamp"] = ((df["datetime"] - pd.Timestamp("1970-01-01")) // pd.Timedelta('1ms')).astype('int64')

    # Map the columns we actually care about – fall back to NaN if a column is missing.
    # 保留买卖5档数据
    col_map = {
        "成交价": "last_price",
        "申买价1": "bid_price_1",
        "申卖价1": "ask_price_1",
        "申买量1": "bid_vol_1",
        "申卖量1": "ask_vol_1",
        "申买价2": "bid_price_2",
        "申卖价2": "ask_price_2",
        "申买量2": "bid_vol_2",
        "申卖量2": "ask_vol_2",
        "申买价3": "bid_price_3",
        "申卖价3": "ask_price_3",
        "申买量3": "bid_vol_3",
        "申卖量3": "ask_vol_3",
        "申买价4": "bid_price_4",
        "申卖价4": "ask_price_4",
        "申买量4": "bid_vol_4",
        "申卖量4": "ask_vol_4",
        "申买价5": "bid_price_5",
        "申卖价5": "ask_price_5",
        "申买量5": "bid_vol_5",
        "申卖量5": "ask_vol_5",
        "成交量": "last_volume",
    }
    for src, dst in col_map.items():
        if src in df.columns:
            df[dst] = pd.to_numeric(df[src], errors="coerce")
        else:
            df[dst] = np.nan

    # 为了向后兼容，保留原来的bid_price和ask_price作为一档的别名
    if df["last_price"].max() > 10000:
        price_columns = [
            "last_price",
            "bid_price_1", "ask_price_1", 
            "bid_price_2", "ask_price_2",
            "bid_price_3", "ask_price_3",
            "bid_price_4", "ask_price_4",
            "bid_price_5", "ask_price_5"
        ]

        # 执行缩放（除以1000）
        for col in price_columns:
            if col in df.columns:
                df[col] = df[col] / 10000
                
    df["bid_price"] = df["bid_price_1"]
    df["ask_price"] = df["ask_price_1"]
    df["bid_vol"] = df["bid_vol_1"]
    df["ask_vol"] = df["ask_vol_1"]

    keep_cols = [
        "timestamp",
        "last_price",
        "bid_price", "ask_price", "bid_vol", "ask_vol",  # 一档（向后兼容）
        "bid_price_1", "ask_price_1", "bid_vol_1", "ask_vol_1",  # 一档
        "bid_price_2", "ask_price_2", "bid_vol_2", "ask_vol_2",  # 二档
        "bid_price_3", "ask_price_3", "bid_vol_3", "ask_vol_3",  # 三档
        "bid_price_4", "ask_price_4", "bid_vol_4", "ask_vol_4",  # 四档
        "bid_price_5", "ask_price_5", "bid_vol_5", "ask_vol_5",  # 五档
        "last_volume",
    ]

    snapshot_df = df[keep_cols].copy()
    snapshot_df.insert(1, "event_type", "snapshot")  # type: ignore[attr-defined]
    return snapshot_df  # type: ignore[return-value]


def _load_trade(trade_file: str) -> pd.DataFrame:
    """Load trade CSV produced by QMT.

    Returns a dataframe with::
        timestamp, event_type, trade_price, trade_qty, trade_side
    """
    df = _read_csv_auto(trade_file)

    if "自然日" not in df.columns or "时间" not in df.columns:
        raise ValueError(f"列 '自然日' 或 '时间' 在文件 {trade_file} 中不存在，无法解析时间")
    df["datetime"] = pd.to_datetime(df["自然日"].astype(str) + " " + df["时间"].astype(str))

    # 仅保留 09:30:00 之后的行情
    _trading_start = _dt.strptime("09:30:00", "%H:%M:%S").time()
    df = df[df["datetime"].dt.time >= _trading_start].copy()

    # 修复时间戳转换 - 使用正确的毫秒时间戳（避免int32溢出）
    df["timestamp"] = ((df["datetime"] - pd.Timestamp("1970-01-01")) // pd.Timedelta('1ms')).astype('int64')

    trade_price_col = "成交价格" if "成交价格" in df.columns else "成交价"
    trade_qty_col = "成交数量" if "成交数量" in df.columns else "成交量"

    df["trade_price"] = pd.to_numeric(df.get(trade_price_col), errors="coerce")
    df["trade_qty"] = pd.to_numeric(df.get(trade_qty_col), errors="coerce")
    df["trade_side"] = df.get("BS标志")  # 保留原始买卖方向符号，可能为 NaN

    trade_df = df[["timestamp", "trade_price", "trade_qty", "trade_side"]].copy()
    trade_df.insert(1, "event_type", "trade")  # type: ignore[attr-defined]
    return trade_df  # type: ignore[return-value]


def _aggregate_same_timestamp(group: pd.DataFrame) -> pd.DataFrame:  # type: ignore[valid-type]
    """Aggregate rows that share the same timestamp.

    新逻辑：
    1. 提取 snapshot 信息（取第一个非空值）
    2. 按 trade_side 分组，每个方向生成一行
    3. 根据交易方向智能填充盘口价格：
       - B方向：ask_price = trade_price，bid_price 用 snapshot 填充
       - S方向：bid_price = trade_price，ask_price 用 snapshot 填充
    4. 如果没有成交，只返回 snapshot 行
    """
    timestamp = group["timestamp"].iloc[0]
    
    safe_first = lambda s: s.dropna().iloc[0] if not s.dropna().empty else np.nan  # noqa: E731

    # 提取 snapshot 信息作为基础（包含5档数据）
    snapshot_base = {
        "timestamp": timestamp,
        "last_price": safe_first(group["last_price"]),
        "bid_price": safe_first(group["bid_price"]),
        "ask_price": safe_first(group["ask_price"]),
        "bid_vol": safe_first(group["bid_vol"]),
        "ask_vol": safe_first(group["ask_vol"]),
        "last_volume": safe_first(group["last_volume"]),
    }

    # 添加5档数据
    for i in range(1, 6):
        snapshot_base.update({
            f"bid_price_{i}": safe_first(group[f"bid_price_{i}"]),
            f"ask_price_{i}": safe_first(group[f"ask_price_{i}"]),
            f"bid_vol_{i}": safe_first(group[f"bid_vol_{i}"]),
            f"ask_vol_{i}": safe_first(group[f"ask_vol_{i}"]),
        })

    # 检查是否有成交数据
    trade_data = group[group["event_type"] == "trade"]  # type: ignore[misc]
    trade_data = trade_data[trade_data["trade_side"].notna()]  # type: ignore[misc]
    trade_data = trade_data[trade_data["trade_qty"].notna()]  # type: ignore[misc]
    
    if trade_data.empty:  # type: ignore[misc]
        # 没有成交，仅返回 snapshot
        result = {
            "event_type": "snapshot",
            "trade_qty": np.nan,
            "trade_price": np.nan,
            "trade_side": np.nan,
        }
        result.update(snapshot_base)
        return pd.DataFrame([result])
    
    # 有成交数据，按方向分组
    results = []
    for side, side_group in trade_data.groupby("trade_side"):  # type: ignore[misc]
        # 计算该方向的汇总信息
        total_qty = side_group["trade_qty"].sum()  # type: ignore[misc]
        if total_qty > 0:
            # 加权平均价格
            weighted_price = np.average(
                side_group["trade_price"].fillna(0),  # type: ignore[misc]
                weights=side_group["trade_qty"].fillna(0)  # type: ignore[misc]
            )
        else:
            weighted_price = np.nan
        
        # 构建该方向的行，从 snapshot_base 开始
        result = snapshot_base.copy()
        result.update({
            "event_type": "snapshot|trade" if "snapshot" in group["event_type"].values else "trade",
            "trade_qty": total_qty,
            "trade_price": weighted_price,
            "trade_side": side,
        })
        
        # 根据交易方向智能填充盘口价格
        if side == "B":
            # B方向成交：成交价就是ask_price（买入时的卖一价）
            result["ask_price"] = weighted_price
            # bid_price 保持 snapshot 的值（如果有的话）
        elif side == "S":
            # S方向成交：成交价就是bid_price（卖出时的买一价）
            result["bid_price"] = weighted_price
            # ask_price 保持 snapshot 的值（如果有的话）
        
        results.append(result)
    
    return pd.DataFrame(results)


def _parallel_groupby_apply(grouped_data, func, n_workers=4):
    """并行执行 groupby.apply 操作"""
    if n_workers <= 1:
        return grouped_data.apply(func)
    
    # 获取所有组
    groups = [group for name, group in grouped_data]
    
    if len(groups) <= n_workers:
        # 组数少于线程数，直接并行处理每个组
        with ThreadPoolExecutor(max_workers=len(groups)) as executor:
            futures = [executor.submit(func, group) for group in groups]
            results = [future.result() for future in futures]
    else:
        # 组数多于线程数，分批处理
        def process_batch(batch_groups):
            return [func(group) for group in batch_groups]
        
        batch_size = len(groups) // n_workers + (1 if len(groups) % n_workers else 0)
        batches = [groups[i:i+batch_size] for i in range(0, len(groups), batch_size)]
        
        with ThreadPoolExecutor(max_workers=n_workers) as executor:
            futures = [executor.submit(process_batch, batch) for batch in batches]
            results = []
            for future in futures:
                results.extend(future.result())
    
    # 合并结果
    return pd.concat(results, ignore_index=True)


def merge_snapshot_trade(snapshot_file: str, trade_file: str, output_file: Optional[str] = None, n_workers: int = 1) -> pd.DataFrame:
    """Merge snapshot and trade csv into a single time-ordered dataframe.

    Parameters
    ----------
    snapshot_file : str
        Path to the snapshot csv.
    trade_file : str
        Path to the trade csv.
    output_file : str, optional
        If provided, the merged dataframe will be written to this path in csv format (utf-8).
    n_workers : int, optional
        Number of threads to use for internal processing. Default is 1 (single-threaded).

    Returns
    -------
    pd.DataFrame
        The merged & aggregated dataframe sorted by timestamp ascending.
    """
    if not os.path.exists(snapshot_file):
        raise FileNotFoundError(snapshot_file)
    if not os.path.exists(trade_file):
        raise FileNotFoundError(trade_file)

    # 并行读取文件
    if n_workers > 1:
        with ThreadPoolExecutor(max_workers=2) as executor:
            snap_future = executor.submit(_load_snapshot, snapshot_file)
            trade_future = executor.submit(_load_trade, trade_file)
            snap_df = snap_future.result()
            trade_df = trade_future.result()
    else:
        snap_df = _load_snapshot(snapshot_file)
        trade_df = _load_trade(trade_file)

    # 为了能够统一聚合，确保两边都有基础列
    # 参考B/S标志的做法：trade有BS标志，snapshot没有，我们不强制统一
    # 同样地：snapshot有5档信息，trade没有，我们也不强制统一
    basic_cols = [
        "last_price",
        "bid_price", "ask_price", "bid_vol", "ask_vol",  # 一档（向后兼容）
        "last_volume",
        "trade_price", "trade_qty", "trade_side",
    ]

    # 确保基础列在两个数据框中都存在
    for col in basic_cols:
        if col not in snap_df:
            snap_df[col] = np.nan
        if col not in trade_df:
            trade_df[col] = np.nan

    # 不对trade数据添加5档列！
    # 保持数据的原始结构：snapshot有5档，trade没有

    # 合并时保持trade数据的原始结构（不包含5档列）
    # 只有snapshot数据包含5档信息
    merged = pd.concat([snap_df, trade_df], ignore_index=True, sort=False)
    merged = merged.sort_values("timestamp").reset_index(drop=True)

    # 确保trade行的5档列保持为NaN（不进行任何填充）
    # 这样5档信息只存在于snapshot行中

    # 只前向填充基本的snapshot信息到trade行（不包含5档数据）
    # trade数据不需要5档信息，5档信息只存在于snapshot中
    basic_snapshot_cols = ["last_price", "bid_price", "ask_price", "bid_vol", "ask_vol", "last_volume"]

    for col in basic_snapshot_cols:
        if col in merged.columns:
            merged[col] = merged[col].ffill()  # 使用新的方法替代 fillna(method='ffill')

    # 聚合同一 timestamp，使用多线程（如果指定）
    if n_workers > 1:
        grouped = merged.groupby("timestamp", group_keys=False)
        merged = _parallel_groupby_apply(grouped, _aggregate_same_timestamp, n_workers)
    else:
        # 使用更现代的方法避免 groupby.apply 的警告
        grouped_results = []
        for timestamp, group in merged.groupby("timestamp"):
            result = _aggregate_same_timestamp(group)
            grouped_results.append(result)
        merged = pd.concat(grouped_results, ignore_index=True)
    
    merged = pd.DataFrame(merged).reset_index(drop=True)
    merged = merged.sort_values("timestamp").reset_index(drop=True)

    if output_file:
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        merged.to_csv(output_file, index=False, encoding="utf-8")
        print(f"保存合并后的数据 -> {output_file} (rows={len(merged)})")

    return merged  # type: ignore[return-value]


def _process_single_file(prefix: str, data_dir: str, out_dir: str, symbol: str, internal_threads: int = 1) -> tuple[str, bool, str]:
    """处理单个文件的函数，用于多线程执行"""
    snapshot_path = os.path.join(data_dir, symbol, "snapshot", f"{prefix}.csv")
    trade_path = os.path.join(data_dir, symbol, "trade", f"{prefix}.csv")
    output_path = os.path.join(out_dir, f"{prefix}.csv")

    try:
        merge_snapshot_trade(snapshot_path, trade_path, output_path, n_workers=internal_threads)
        return prefix, True, f"成功处理 {prefix}"
    except Exception as e:
        return prefix, False, f"处理 {prefix} 失败: {e}"


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Merge QMT snapshot & trade csv into unified orderbook-like dataset.")
    parser.add_argument("symbol_date", nargs="*", help="文件前缀，如 sh513120_20240902。不需要扩展名。")
    parser.add_argument(
        "--data-dir",
        default="backtest_data",
        help="数据根目录，默认为 ./backtest_data",
    )
    parser.add_argument("--symbol", default="ETF_513120", help="标的代码，默认为 ETF_513120")
    parser.add_argument("--out-dir", default=None, help="输出目录，默认为 backtest_data/{symbol}/merged")
    parser.add_argument("--all", action="store_true", help="自动遍历 snapshot 目录，批量合并所有文件")
    parser.add_argument("--file-threads", type=int, default=1, help="文件级并行线程数，默认为1（逐个处理文件）")
    parser.add_argument("--internal-threads", type=int, default=1, help="单文件内部处理线程数，默认为1")

    args = parser.parse_args()

    # 设置默认输出目录
    if args.out_dir is None:
        args.out_dir = os.path.join(args.data_dir, args.symbol, "merged")

    prefixes = args.symbol_date
    if args.all or not prefixes:
        # 自动扫描 snapshot 目录
        snapshot_dir = os.path.join(args.data_dir, args.symbol, "snapshot")
        trade_dir = os.path.join(args.data_dir, args.symbol, "trade")
        
        if not os.path.exists(snapshot_dir):
            raise FileNotFoundError(f"Snapshot 目录不存在: {snapshot_dir}")
        if not os.path.exists(trade_dir):
            raise FileNotFoundError(f"Trade 目录不存在: {trade_dir}")
        
        # 获取所有 snapshot 文件的前缀
        snapshot_files = {os.path.splitext(f)[0] for f in os.listdir(snapshot_dir) if f.endswith(".csv")}
        # 获取所有 trade 文件的前缀
        trade_files = {os.path.splitext(f)[0] for f in os.listdir(trade_dir) if f.endswith(".csv")}
        
        # 只处理同时存在 snapshot 和 trade 文件的前缀
        prefixes = sorted(list(snapshot_files & trade_files))
        
        if not prefixes:
            print("没有找到匹配的 snapshot 和 trade 文件对")
            exit(1)
        
        print(f"找到 {len(prefixes)} 个文件对需要处理")
        missing_snapshot = trade_files - snapshot_files
        missing_trade = snapshot_files - trade_files
        if missing_snapshot:
            print(f"警告: 以下文件只有 trade 没有 snapshot: {sorted(missing_snapshot)}")
        if missing_trade:
            print(f"警告: 以下文件只有 snapshot 没有 trade: {sorted(missing_trade)}")

    # 确保输出目录存在
    os.makedirs(args.out_dir, exist_ok=True)
    
    # 使用多线程处理
    start_time = time.time()
    success_count = 0
    fail_count = 0
    
    if args.file_threads <= 1 or len(prefixes) == 1:
        # 单线程文件处理或只有一个文件
        for prefix in prefixes:
            print(f"处理 {prefix} ...")
            _, success, message = _process_single_file(prefix, args.data_dir, args.out_dir, args.symbol, args.internal_threads)
            print(f"  -> {message}")
            if success:
                success_count += 1
            else:
                fail_count += 1
    else:
        # 多线程文件处理
        print(f"使用 {args.file_threads} 个线程并行处理 {len(prefixes)} 个文件...")
        if args.internal_threads > 1:
            print(f"每个文件内部使用 {args.internal_threads} 个线程")
        
        with ThreadPoolExecutor(max_workers=args.file_threads) as executor:
            # 提交所有任务
            future_to_prefix = {
                executor.submit(_process_single_file, prefix, args.data_dir, args.out_dir, args.symbol, args.internal_threads): prefix
                for prefix in prefixes
            }
            
            # 处理完成的任务
            for future in as_completed(future_to_prefix):
                prefix = future_to_prefix[future]
                try:
                    _, success, message = future.result()
                    print(f"  -> {message}")
                    if success:
                        success_count += 1
                    else:
                        fail_count += 1
                except Exception as e:
                    print(f"  -> 处理 {prefix} 时发生异常: {e}")
                    fail_count += 1
    
    elapsed_time = time.time() - start_time
    print(f"\n=== 处理完成 ===")
    print(f"总文件数: {len(prefixes)}")
    print(f"成功: {success_count}")
    print(f"失败: {fail_count}")
    print(f"用时: {elapsed_time:.2f} 秒")
    if len(prefixes) > 1:
        print(f"平均每文件: {elapsed_time/len(prefixes):.2f} 秒") 
# python merge_snapshot_trade.py --all --symbol ETF_513120