import asyncio  
import websockets  
import json  
import csv  
import sys  
import os 

SAVE_INTERVAL = 10  
MAX_FILE_SIZE = 1024* 1024* 500

async def fetch_2local_order_book(symbol, level = 10):  
    uri = f"wss://fstream.binance.com/stream?streams={symbol}@depth{level}@500ms"  
    
    file_number = 0  
    save_path = ""
    if not os.path.exists(save_path):  
        os.makedirs(save_path) 
    csv_file = open(os.path.join(save_path, f"{symbol}_order_book_data_{file_number}.csv"), "a", newline="") 
    csv_writer = csv.writer(csv_file)  
    
   
    if csv_file.tell() == 0:  
        header = ["Timestamp"]  
        for i in range(1, level + 1):  
            header.extend([f"bid_price_{i}", f"bid_qty_{i}"])  
        for i in range(1, level + 1):
            header.extend([f"aks_price_{i}", f"ask_qty_{i}"])  
        csv_writer.writerow(header)  
    
    
    data_cache = []  
    last_save_time = asyncio.get_event_loop().time()  
    
    async with websockets.connect(uri) as ws:  
        while True:  
            response = await ws.recv() 
            data = json.loads(response)  
            timestamp = data["data"]["E"]  
            bids = data["data"]["b"] 
            asks = data["data"]["a"] 
            
            row = [timestamp]  
            for bid in bids[:level]:  
                row.extend([bid[0], bid[1]])  
            for ask in asks[:level]: 
                row.extend([ask[0], ask[1]])  
            
            data_cache.append(row)  
              
            current_time = asyncio.get_event_loop().time()  
            if current_time - last_save_time >= SAVE_INTERVAL:  
                # 将缓存数据写入 CSV 文件  
                csv_writer.writerows(data_cache)  
                csv_file.flush()  
                print(f"Data saved at {timestamp}")  
            
                data_cache = []  
                last_save_time = current_time  
                

                if os.path.getsize(csv_file.name) >= MAX_FILE_SIZE:  
                    csv_file.close()   
                    file_number += 1  
                    csv_file = open(f"{symbol}_order_book_data_{file_number}.csv", "a", newline="")  
                    csv_writer = csv.writer(csv_file)  
                    
                    # 写入新文件的表头  
                    csv_writer.writerow(header)  
                    print(f"New file created: {csv_file.name}")  

async def main(symbol):  
    await fetch_2local_order_book(symbol)  

if __name__ == "__main__":  
    symbol = "trumpusdt"
    asyncio.run(main(symbol))  