import numpy as np
import datetime

class DollarBarGenerator:
    """
    按成交额阈值生成Dollar Bar
    """
    def __init__(self, dollar_threshold):
        self.dollar_threshold = dollar_threshold
        self.reset()

    def reset(self):
        self.current_bar = {
            'open': None,
            'high': -np.inf,
            'low': np.inf,
            'close': None,
            'volume': 0,
            'dollar': 0,
            'timestamp': None
        }
        self.bars = []

    def update(self, price, volume, dollar, timestamp):
        if self.current_bar['open'] is None:
            self.current_bar['open'] = price
            self.current_bar['timestamp'] = timestamp
        self.current_bar['high'] = max(self.current_bar['high'], price)
        self.current_bar['low'] = min(self.current_bar['low'], price)
        self.current_bar['close'] = price
        self.current_bar['volume'] += volume
        self.current_bar['dollar'] += dollar
        if self.current_bar['dollar'] >= self.dollar_threshold:
            self.bars.append(self.current_bar.copy())
            self.reset()

    def get_bars(self):
        return self.bars

class TripleMA:
    """
    三均线系统
    """
    def __init__(self, short=5, mid=15, long=30):
        self.short = short
        self.mid = mid
        self.long = long
        self.close_list = []

    def update(self, close):
        self.close_list.append(close)
        if len(self.close_list) > self.long:
            self.close_list.pop(0)

    def get_ma(self):
        arr = np.array(self.close_list)
        ma_short = arr[-self.short:].mean() if len(arr) >= self.short else np.nan
        ma_mid = arr[-self.mid:].mean() if len(arr) >= self.mid else np.nan
        ma_long = arr[-self.long:].mean() if len(arr) >= self.long else np.nan
        return ma_short, ma_mid, ma_long

    def is_strong_down(self):
        ma_short, ma_mid, ma_long = self.get_ma()
        # 短期均线下穿中长期均线
        if np.isnan(ma_short) or np.isnan(ma_mid) or np.isnan(ma_long):
            return False
        return ma_short < ma_mid < ma_long

class QMT_DollarBar_MA_Strategy:
    """
    QMT风格的Dollar Bar三均线下跌信号检测
    """
    def __init__(self, dollar_threshold=1e6, short=5, mid=15, long=30, volume_ratio=2):
        self.bar_gen = DollarBarGenerator(dollar_threshold)
        self.ma = TripleMA(short, mid, long)
        self.last_bar = None
        self.volume_ratio = volume_ratio
        self.log = []

    def on_tick(self, price, volume, timestamp):
        dollar = price * volume
        self.bar_gen.update(price, volume, dollar, timestamp)
        bars = self.bar_gen.get_bars()
        if len(bars) == 0:
            return None
        bar = bars[-1]
        if self.last_bar is not None:
            # 计算成交额放大倍数
            prev_dollar = self.last_bar['dollar']
            curr_dollar = bar['dollar']
            dollar_increase = curr_dollar > prev_dollar * self.volume_ratio
        else:
            dollar_increase = False
        self.ma.update(bar['close'])
        signal = self.ma.is_strong_down() and dollar_increase
        if signal:
            msg = f"[{datetime.datetime.fromtimestamp(bar['timestamp']/1000)}] 强势下跌信号: close={bar['close']:.3f}, 短中长均线={self.ma.get_ma()}, 成交额={bar['dollar']:.0f}"
            print(msg)
            self.log.append(msg)
        self.last_bar = bar
        return signal 