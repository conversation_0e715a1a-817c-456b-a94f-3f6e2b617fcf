#encoding:gbk
import pandas as pd
import numpy as np
import datetime

"""
ETF做市标的筛选策略
通过分析成交量、价差、波动率、趋势等指标筛选适合做市的ETF
"""

class ScreenerData():
    pass

# 创建全局数据存储实例
S = ScreenerData()

def init(C):
    """初始化函数"""
    print("=" * 60)
    print("ETF做市标的筛选策略启动")
    print("=" * 60)
    
    # 候选ETF列表
    S.candidate_etfs = [
        '513120.SH', '159776.SZ', '159615.SZ', '513200.SH', '513060.SH', '159892.SZ', '513280.SH', '159718.SZ',
        '513360.SH', '513700.SH', '513020.SH', '501021.SH', '513980.SH', '513860.SH', '513090.SH', '501311.SH',
        '513530.SH', '513320.SH', '159750.SZ', '513150.SH', '159788.SZ', '159747.SZ', '159960.SZ', '159954.SZ',
        '513680.SH', '159792.SZ', '159850.SZ', '159712.SZ', '159823.SZ', '160416.SH', '513770.SH', '513160.SH',
        '513010.SH', '510900.SH', '161128.SH', '159726.SZ', '159822.SZ', '501025.SH', '513590.SH', '159751.SZ',
        '159607.SZ', '513890.SH', '513990.SH', '513080.SH', '164705.SH', '159866.SZ', '513260.SH', '164824.SH',
        '513580.SH', '160322.SH', '518860.SH', '513690.SH', '511380.SH', '511260.SH', '513300.SH', '511060.SH',
        '511180.SH', '511970.SH', '511020.SH', '511270.SH', '511950.SH', '511220.SH', '511910.SH', '159832.SZ',
        '511600.SH', '511620.SH', '511010.SH', '159816.SZ', '511930.SH', '511670.SH', '159972.SZ', '511880.SH',
        '511920.SH', '511660.SH', '511820.SH', '511800.SH', '511830.SH', '511700.SH', '511900.SH', '511850.SH',
        '511990.SH', '511690.SH', '511360.SH', '511650.SH', '161116.SH', '160717.SH', '159741.SZ', '159740.SZ',
        '159711.SZ', '159005.SZ', '513900.SH', '513660.SH', '513380.SH', '513180.SH', '501302.SH', '159001.SZ'
    ]
    
    # 筛选标准
    S.min_volume = 10000000      # 最小日成交额（1000万）
    S.max_spread_pct = 0.005     # 最大买卖价差比例（0.5%）
    S.min_price = 0.5            # 最小价格
    S.max_price = 50.0           # 最大价格
    S.min_volatility = 0.015     # 最小日波动率（1.5%）
    S.max_volatility = 0.08      # 最大日波动率（8%）
    S.min_trend_days = 3         # 最少上涨天数
    S.min_trend_return = 0.02    # 最小趋势涨幅（2%）
    S.lookback_days = 10         # 回看天数
    
    # 状态变量
    S.last_screen_date = None
    S.qualified_etfs = []
    S.screening_results = {}
    
    # 设置股票池（选择部分ETF进行订阅，避免订阅过多）
    test_etfs = S.candidate_etfs[:20]  # 只订阅前20个进行测试
    C.set_universe(S.candidate_etfs)
    
    print(f"候选ETF总数: {len(S.candidate_etfs)}")
    print(f"测试ETF数量: {len(test_etfs)}")
    print("初始化完成")

def get_etf_market_data(etf_code, C):
    """获取ETF市场数据"""
    try:
        # 获取实时行情数据
        tick_data = C.get_full_tick([etf_code])
        if etf_code not in tick_data:
            return None

        tick = tick_data[etf_code]
        current_price = tick['lastPrice']
        bid_price = tick['bidPrice'][0]
        ask_price = tick['askPrice'][0]

        # 使用 get_market_data_ex 获取历史数据
        hist_data = C.get_market_data_ex(
            fields=['close', 'volume', 'amount', 'high', 'low'],
            stock_code=[etf_code],
            period='1d',
            count=S.lookback_days,
            dividend_type='front'
        )

        if not hist_data or etf_code not in hist_data:
            print(f"    无法获取 {etf_code} 历史数据")
            return None

        hist = hist_data[etf_code]

        # 检查数据长度
        if len(hist.get('close', [])) < 5:
            print(f"    {etf_code} 历史数据不足")
            return None
        
        # 计算买卖价差
        if bid_price > 0 and ask_price > 0:
            spread_pct = (ask_price - bid_price) / current_price if current_price > 0 else 999
        else:
            spread_pct = 999
        
        # 计算平均成交额
        avg_amount = np.mean(hist['amount']) if len(hist['amount']) > 0 else 0
        
        # 计算波动率
        if len(hist['close']) > 1:
            returns = np.diff(hist['close']) / hist['close'][:-1]
            volatility = np.std(returns) if len(returns) > 0 else 0
        else:
            volatility = 0
        
        # 分析趋势
        trend_analysis = analyze_trend(hist['close'])
        
        return {
            'code': etf_code,
            'price': current_price,
            'spread_pct': spread_pct,
            'avg_amount': avg_amount,
            'volatility': volatility,
            'trend_up_days': trend_analysis['up_days'],
            'trend_return': trend_analysis['total_return'],
            'recent_trend': trend_analysis['recent_trend']
        }
        
    except Exception as e:
        print(f"获取 {etf_code} 数据失败: {e}")
        return None

def analyze_trend(close_prices):
    """分析价格趋势"""
    try:
        if len(close_prices) < 2:
            return {'up_days': 0, 'total_return': 0, 'recent_trend': 'unknown'}
        
        # 计算每日涨跌
        daily_changes = np.diff(close_prices)
        up_days = np.sum(daily_changes > 0)
        
        # 计算总收益率
        total_return = (close_prices[-1] / close_prices[0] - 1) if close_prices[0] > 0 else 0
        
        # 判断近期趋势（最近3天）
        if len(close_prices) >= 3:
            recent_return = (close_prices[-1] / close_prices[-3] - 1) if close_prices[-3] > 0 else 0
            if recent_return > 0.01:
                recent_trend = 'strong_up'
            elif recent_return > 0:
                recent_trend = 'up'
            elif recent_return > -0.01:
                recent_trend = 'sideways'
            else:
                recent_trend = 'down'
        else:
            recent_trend = 'unknown'
        
        return {
            'up_days': up_days,
            'total_return': total_return,
            'recent_trend': recent_trend
        }
        
    except Exception as e:
        print(f"趋势分析失败: {e}")
        return {'up_days': 0, 'total_return': 0, 'recent_trend': 'unknown'}

def evaluate_etf(market_data):
    """评估ETF是否适合做市"""
    if market_data is None:
        return False, "无市场数据"
    
    reasons = []
    
    # 检查价格范围
    if market_data['price'] < S.min_price:
        reasons.append(f"价格过低({market_data['price']:.3f})")
    if market_data['price'] > S.max_price:
        reasons.append(f"价格过高({market_data['price']:.3f})")
    
    # 检查成交额
    if market_data['avg_amount'] < S.min_volume:
        reasons.append(f"成交额不足({market_data['avg_amount']:.0f})")
    
    # 检查买卖价差
    if market_data['spread_pct'] > S.max_spread_pct:
        reasons.append(f"价差过大({market_data['spread_pct']:.4f})")
    
    # 检查波动率
    if market_data['volatility'] < S.min_volatility:
        reasons.append(f"波动率过低({market_data['volatility']:.4f})")
    if market_data['volatility'] > S.max_volatility:
        reasons.append(f"波动率过高({market_data['volatility']:.4f})")
    
    # 检查趋势方向
    if market_data['trend_up_days'] < S.min_trend_days:
        reasons.append(f"上涨天数不足({market_data['trend_up_days']}天)")
    
    if market_data['trend_return'] < S.min_trend_return:
        reasons.append(f"趋势涨幅不足({market_data['trend_return']:.3f})")
    
    # 检查近期趋势
    if market_data['recent_trend'] in ['down']:
        reasons.append(f"近期趋势向下({market_data['recent_trend']})")
    
    is_qualified = len(reasons) == 0
    reason = "合格" if is_qualified else "; ".join(reasons)
    
    return is_qualified, reason

def screen_etfs(C):
    """筛选ETF"""
    print("开始筛选ETF做市标的...")
    print("=" * 80)
    
    qualified_count = 0
    test_etfs = S.candidate_etfs[:20]  # 只测试前20个
    
    for i, etf_code in enumerate(test_etfs):
        print(f"[{i+1}/{len(test_etfs)}] 检查 {etf_code}...")
        
        # 获取市场数据
        market_data = get_etf_market_data(etf_code, C)
        
        # 评估是否合格
        is_qualified, reason = evaluate_etf(market_data)
        
        # 记录结果
        S.screening_results[etf_code] = {
            'qualified': is_qualified,
            'reason': reason,
            'data': market_data
        }
        
        if is_qualified:
            S.qualified_etfs.append(etf_code)
            qualified_count += 1
            print(f"  ✅ 合格 - 价格: {market_data['price']:.3f}, 价差: {market_data['spread_pct']:.4f}, 波动率: {market_data['volatility']:.3f}, 趋势: {market_data['recent_trend']}")
        else:
            print(f"  ❌ 不合格 - {reason}")
    
    print("=" * 80)
    print(f"筛选完成: {qualified_count}/{len(test_etfs)} 个标的合格")
    
    return S.qualified_etfs

def print_results():
    """打印筛选结果"""
    print("\n" + "=" * 80)
    print("ETF做市标的筛选结果")
    print("=" * 80)
    
    if not S.qualified_etfs:
        print("❌ 没有找到合格的做市标的")
        return
    
    print(f"✅ 找到 {len(S.qualified_etfs)} 个合格的做市标的:")
    print()
    
    # 按成交额排序
    qualified_data = []
    for etf in S.qualified_etfs:
        data = S.screening_results[etf]['data']
        qualified_data.append((etf, data))
    
    qualified_data.sort(key=lambda x: x[1]['avg_amount'], reverse=True)
    
    print(f"{'代码':<12} {'价格':<8} {'价差%':<8} {'波动率%':<10} {'趋势':<12} {'成交额(万)':<12}")
    print("-" * 80)
    
    for etf, data in qualified_data:
        trend_display = f"{data['recent_trend']}({data['trend_return']*100:+.1f}%)"
        print(f"{etf:<12} {data['price']:<8.3f} {data['spread_pct']*100:<8.3f} {data['volatility']*100:<10.2f} {trend_display:<12} {data['avg_amount']/10000:<12.0f}")
    
    print("\n推荐做市标的（按成交额排序）:")
    for i, (etf, data) in enumerate(qualified_data[:10]):
        print(f"{i+1:2d}. {etf} - 成交额: {data['avg_amount']/10000:.0f}万, 波动率: {data['volatility']*100:.2f}%, 趋势: {data['recent_trend']}")

def handlebar(C):
    """主策略函数 - 随时可以运行"""
    # 跳过历史K线
    if not C.is_last_bar():
        return

    now = datetime.datetime.now()
    current_date = now.strftime('%Y-%m-%d')

    print(f"[{now.strftime('%H:%M:%S')}] handlebar被调用，开始执行ETF筛选...")

    # 清空之前的结果
    S.qualified_etfs = []
    S.screening_results = {}

    # 执行筛选
    qualified_etfs = screen_etfs(C)

    # 打印结果
    print_results()

    print("ETF筛选完成")
