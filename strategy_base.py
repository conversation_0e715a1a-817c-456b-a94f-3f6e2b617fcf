#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略基类 - 定义策略接口标准
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass
import pandas as pd


@dataclass
class StrategyConfig:
    """策略配置基类"""
    pass


@dataclass
class MarketData:
    """市场数据结构"""
    timestamp: int
    bid_price: float
    ask_price: float
    last_price: float
    bid_volume: float
    ask_volume: float
    # 可选的深度数据
    bid_prices: Optional[list] = None
    ask_prices: Optional[list] = None
    bid_volumes: Optional[list] = None
    ask_volumes: Optional[list] = None
    # 其他数据
    extra_data: Optional[Dict[str, Any]] = None


@dataclass
class Signal:
    """交易信号"""
    action: str  # 'buy', 'sell', 'hold'
    price: float
    quantity: int
    reason: str = ""
    confidence: float = 1.0


class StrategyBase(ABC):
    """策略基类 - 所有策略必须继承此类"""
    
    def __init__(self, config: StrategyConfig):
        self.config = config
        self.initialized = False
    
    @abstractmethod
    def initialize(self, initial_data: pd.DataFrame) -> None:
        """
        策略初始化
        Args:
            initial_data: 初始历史数据，用于计算指标等
        """
        pass
    
    @abstractmethod
    def on_market_data(self, data: MarketData) -> Optional[Signal]:
        """
        处理市场数据，生成交易信号
        Args:
            data: 当前市场数据
        Returns:
            交易信号或None
        """
        pass
    
    @abstractmethod
    def on_trade_executed(self, price: float, quantity: int, side: str, timestamp: int) -> None:
        """
        交易执行回调
        Args:
            price: 成交价格
            quantity: 成交数量
            side: 交易方向 'buy'/'sell'
            timestamp: 成交时间
        """
        pass
    
    @abstractmethod
    def get_optimization_params(self) -> Dict[str, Tuple[float, float]]:
        """
        获取需要优化的参数及其范围
        Returns:
            参数名 -> (最小值, 最大值) 的字典
        """
        pass
    
    @abstractmethod
    def update_params(self, params: Dict[str, Any]) -> None:
        """
        更新策略参数（用于优化）
        Args:
            params: 新的参数值
        """
        pass
    
    def reset(self) -> None:
        """重置策略状态（用于新的回测）"""
        self.initialized = False
    
    def get_strategy_name(self) -> str:
        """获取策略名称"""
        return self.__class__.__name__
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': self.get_strategy_name(),
            'config': self.config.__dict__ if hasattr(self.config, '__dict__') else str(self.config),
            'optimization_params': self.get_optimization_params()
        }


class StrategyManager:
    """策略管理器 - 负责策略的加载和管理"""
    
    def __init__(self):
        self.strategies = {}
    
    def register_strategy(self, name: str, strategy_class: type, config_class: type):
        """注册策略"""
        self.strategies[name] = {
            'strategy_class': strategy_class,
            'config_class': config_class
        }
    
    def create_strategy(self, name: str, config_params: Dict[str, Any]) -> StrategyBase:
        """创建策略实例"""
        if name not in self.strategies:
            raise ValueError(f"未知策略: {name}")
        
        strategy_info = self.strategies[name]
        config = strategy_info['config_class'](**config_params)
        strategy = strategy_info['strategy_class'](config)
        
        return strategy
    
    def list_strategies(self) -> list:
        """列出所有可用策略"""
        return list(self.strategies.keys())


# 全局策略管理器实例
strategy_manager = StrategyManager()


def register_strategy(name: str):
    """策略注册装饰器"""
    def decorator(strategy_class):
        # 自动推断配置类名
        config_class_name = f"{strategy_class.__name__}Config"
        
        # 尝试从策略类的模块中找到配置类
        import inspect
        module = inspect.getmodule(strategy_class)
        if hasattr(module, config_class_name):
            config_class = getattr(module, config_class_name)
        else:
            # 如果没找到，使用基础配置类
            config_class = StrategyConfig
        
        strategy_manager.register_strategy(name, strategy_class, config_class)
        return strategy_class
    
    return decorator
