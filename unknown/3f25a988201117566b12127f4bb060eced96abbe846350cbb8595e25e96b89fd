import pandas as pd  

def tradetick_to_volume_bars(df, volume_threshold=None, bar_in_day = 900):  
    """  
    将交易数据聚合成 Volume Bar。  

    参数:  
    df (pd.DataFrame): 包含交易数据的 DataFrame，必须包含以下列：  
        - price: 交易价格  
        - qty: 交易量  
        - timestamp: 交易时间戳  
        - is_buyer_maker: 是否为买方挂单（1 表示卖方为主，0 表示买方为主）  
    volume_threshold (float): 每个 Volume Bar 的交易量阈值。如果为 None，则使用数据的中位数 * 10。  

    返回:  
    pd.DataFrame: 包含 Volume Bar 的 DataFrame，列包括：  
        - open: Bar 的开盘价  
        - high: Bar 的最高价  
        - low: Bar 的最低价  
        - close: Bar 的收盘价  
        - volume: Bar 的总交易量  
        - buy_volume: Bar 的买入交易量  
        - sell_volume: Bar 的卖出交易量  
        - timestamp_start: Bar 的开始时间  
        - timestamp_end: Bar 的结束时间  
    """  
    # 如果未提供 volume_threshold，则使用数据的中位数 * 10  
    if volume_threshold is None:  
        temp  = df[df['timestamp'] - df['timestamp'].iloc[0] < 24 * 3600 * 1000]
        volume_threshold = round(temp['qty'].sum() / bar_in_day)

    # 初始化变量  
    volume_sum = 0  
    bars = []  
    current_bar = {  
        'open': None, 'high': None, 'low': None, 'close': None,  
        'volume': 0, 'buy_volume': 0, 'sell_volume': 0,  
        'timestamp_start': None, 'timestamp_end': None  
    }  

    # 遍历数据  
    for i, row in df.iterrows():  
        price = row['price']  
        qty = row['qty']  
        timestamp = row['timestamp']  
        is_buyer_maker = row['is_buyer_maker']  

        # 初始化当前 Bar 的开盘价和时间  
        if current_bar['open'] is None:  
            current_bar['open'] = price  
            current_bar['timestamp_start'] = timestamp  

        # 更新当前 Bar 的最高价、最低价和收盘价  
        current_bar['high'] = max(current_bar['high'], price) if current_bar['high'] is not None else price  
        current_bar['low'] = min(current_bar['low'], price) if current_bar['low'] is not None else price  
        current_bar['close'] = price  

        # 处理交易量拆分  
        remaining_qty = qty  
        while remaining_qty > 0:  
            # 计算当前 Bar 还可以容纳的交易量  
            available_qty = volume_threshold - volume_sum  

            # 如果当前 Bar 还能容纳全部交易量  
            if remaining_qty <= available_qty:  
                volume_sum += remaining_qty  
                current_bar['volume'] += remaining_qty  

                # 区分买入和卖出交易量  
                if is_buyer_maker == 1:  
                    current_bar['sell_volume'] += remaining_qty  # 卖方为主  
                else:  
                    current_bar['buy_volume'] += remaining_qty  # 买方为主  

                remaining_qty = 0  
            else:  
                # 当前 Bar 只能容纳部分交易量  
                volume_sum += available_qty  
                current_bar['volume'] += available_qty  

                # 区分买入和卖出交易量  
                if is_buyer_maker == 1:  
                    current_bar['sell_volume'] += available_qty  # 卖方为主  
                else:  
                    current_bar['buy_volume'] += available_qty  # 买方为主  

                # 生成一个 Bar  
                current_bar['timestamp_end'] = timestamp  
                bars.append(current_bar)  

                # 重置当前 Bar  
                volume_sum = 0  
                current_bar = {  
                    'open': price, 'high': price, 'low': price, 'close': price,  
                    'volume': 0, 'buy_volume': 0, 'sell_volume': 0,  
                    'timestamp_start': timestamp, 'timestamp_end': None  
                }  

                # 剩余交易量  
                remaining_qty -= available_qty  

    # 如果最后一个 Bar 有数据，添加到结果中  
    if current_bar['volume'] > 0:  
        current_bar['timestamp_end'] = df.iloc[-1]['timestamp']  
        bars.append(current_bar)  

    bars_df = pd.DataFrame(bars)  
    return bars_df

def tradetick_to_dollar_bars(df, dollar_threshold=None):  
    """  
    将交易数据聚合成 Dollar Bar。  

    参数:  
    df (pd.DataFrame): 包含交易数据的 DataFrame，必须包含以下列：  
        - price: 交易价格  
        - qty: 交易量  
        - timestamp: 交易时间戳  
        - is_buyer_maker: 是否为买方挂单（1 表示卖方为主，0 表示买方为主）  
    dollar_threshold (float): 每个 Dollar Bar 的交易金额阈值。如果为 None，则使用前 200 项数据的中位数 * 10。  

    返回:  
    pd.DataFrame: 包含 Dollar Bar 的 DataFrame，列包括：  
        - open: Bar 的开盘价  
        - high: Bar 的最高价  
        - low: Bar 的最低价  
        - close: Bar 的收盘价  
        - volume: Bar 的总交易量  
        - dollar_value: Bar 的总交易金额  
        - buy_dollar_value: Bar 的买入交易金额  
        - sell_dollar_value: Bar 的卖出交易金额  
        - timestamp_start: Bar 的开始时间  
        - timestamp_end: Bar 的结束时间  
    """  
    # 如果未提供 dollar_threshold，则使用前 200 项数据的中位数 * 10  
    if dollar_threshold is None:  
        dollar_threshold = (df['price'] * df['qty']).head(200).median() * 10  

    # 初始化变量  
    dollar_sum = 0  
    bars = []  
    current_bar = {  
        'open': None, 'high': None, 'low': None, 'close': None,  
        'volume': 0, 'dollar_value': 0, 'buy_dollar_value': 0, 'sell_dollar_value': 0,  
        'timestamp_start': None, 'timestamp_end': None  
    }  

    # 遍历数据  
    for i, row in df.iterrows():  
        price = row['price']  
        qty = row['qty']  
        timestamp = row['timestamp']  
        is_buyer_maker = row['is_buyer_maker']  
        dollar_value = price * qty  # 当前交易金额  

        # 初始化当前 Bar 的开盘价和时间  
        if current_bar['open'] is None:  
            current_bar['open'] = price  
            current_bar['timestamp_start'] = timestamp  

        # 更新当前 Bar 的最高价、最低价和收盘价  
        current_bar['high'] = max(current_bar['high'], price) if current_bar['high'] is not None else price  
        current_bar['low'] = min(current_bar['low'], price) if current_bar['low'] is not None else price  
        current_bar['close'] = price  

        # 累加总交易金额和交易量  
        dollar_sum += dollar_value  
        current_bar['dollar_value'] += dollar_value  
        current_bar['volume'] += qty  

        # 区分买入和卖出交易金额  
        if is_buyer_maker == 1:  
            current_bar['sell_dollar_value'] += dollar_value  # 卖方为主  
        else:  
            current_bar['buy_dollar_value'] += dollar_value  # 买方为主  

        # 如果达到交易金额阈值，生成一个 Bar  
        if dollar_sum >= dollar_threshold:  
            current_bar['timestamp_end'] = timestamp  
            bars.append(current_bar)  

            # 重置变量  
            dollar_sum = 0  
            current_bar = {  
                'open': None, 'high': None, 'low': None, 'close': None,  
                'volume': 0, 'dollar_value': 0, 'buy_dollar_value': 0, 'sell_dollar_value': 0,  
                'timestamp_start': None, 'timestamp_end': None  
            }  

    # 将结果转换为 DataFrame  
    bars_df = pd.DataFrame(bars)  
    return bars_df  

def tradetick_to_volume_bars_nparray(data_array, volume_threshold=None, bar_in_day = 900): 
    '''
    如果是回测 nparray dfcc = [dfc.price, buyer_maker, dfc.timestamp, qty]
    这种格式
    参上
    '''
    df = pd.DataFrame(data_array, columns=['price', 'is_buyer_maker', 'timestamp', 'qty'])
    return tradetick_to_volume_bars(df, volume_threshold, bar_in_day = bar_in_day)

class volume_bar_system:  
    def __init__(self, volume_threshold, max_bars = 900):  
        """  
        初始化 Volume Bar 合成器  
        :param volume_threshold: 交易量阈值  
        :param max_bars: 最大 Bar 数量限制  
        """  
        self.volume_threshold = volume_threshold  
        self.max_bars = max_bars  
        self.reset()  
        self.bars_df = pd.DataFrame(columns=[  # 用于存储所有合成的 Bar  
             'open', 'high', 'low', 'close',  
            'volume', 'buy_volume', 'sell_volume','vpin','timestamp_start', 'timestamp_end',
        ])  

    def reset(self):  
        """重置状态，开始合成新的 Bar"""  
        self.current_bar = {  
            'open': None,             # 开盘价  
            'high': None,             # 最高价  
            'low': None,              # 最低价  
            'close': None,            # 收盘价  
            'volume': 0,              # 总交易量  
            'buy_volume': 0,          # 买方交易量  
            'sell_volume': 0,         # 卖方交易量              
            'vpin': None,             #   
            'timestamp_start': None,  # Bar 的起始时间戳  
            'timestamp_end': None,    # Bar 的结束时间戳  
        }  

    def process_trade(self, trade):  
        """  
        处理单条交易信息  
        :param trade: 单条交易信息，格式为 {  
            'timestamp': int,  
            'price': float,  
            'is_buyer_maker': bool,  
            'side': str,  # 'buy' 或 'sell'  
            'qty': float  
        }  
        :return: 如果达到阈值，返回合成的 Bar；否则返回 None  
        """  
        # 如果是第一条交易，初始化 Bar 的起始时间和开盘价  
        if self.current_bar['timestamp_start'] is None:  
            self.current_bar['timestamp_start'] = trade['timestamp']  
            self.current_bar['open'] = trade['price']  
            self.current_bar['high'] = trade['price']  
            self.current_bar['low'] = trade['price']  

        # 更新最高价和最低价  
        self.current_bar['high'] = max(self.current_bar['high'], trade['price'])  
        self.current_bar['low'] = min(self.current_bar['low'], trade['price'])  

        # 更新收盘价  
        self.current_bar['close'] = trade['price']  

        # 更新总交易量  
        self.current_bar['volume'] += trade['qty']  

        # 更新买方或卖方交易量  
        if trade.get('side') == 'buy' or not trade.get('is_buyer_maker', False):  
            self.current_bar['buy_volume'] += trade['qty']  
        else:  
            self.current_bar['sell_volume'] += trade['qty'] 

        # 更新 Bar 的结束时间  
        self.current_bar['timestamp_end'] = trade['timestamp']  

        # 判断是否达到阈值  
        if self.current_bar['volume'] >= self.volume_threshold:  
            # 将 Bar 添加到 DataFrame 中  
            self.current_bar['vpin'] = abs(self.current_bar['buy_volume'] - self.current_bar['sell_volume']) / self.current_bar['volume']
            if not self.bars_df.empty:  
                self.bars_df = pd.concat([self.bars_df, pd.DataFrame([self.current_bar])], ignore_index=True)  
            else:  
                self.bars_df = pd.DataFrame([self.current_bar])
            print(f"volume_bar size:{len(self.bars_df)}")
            # 如果 Bar 数量超过限制，移除最早的 Bar  
            if len(self.bars_df) > self.max_bars:  
                self.bars_df = self.bars_df.iloc[1:]  
            # 重置状态，开始合成新的 Bar  
            self.reset()  
        #     return self.bars_df.iloc[-1]  # 返回最新合成的 Bar  
        # else:  
        #     return None  

    def get_bars(self):  
        return self.bars_df  

    def get_bars_length(self): 
        return len(self.bars_df)  

    def update_parameters(self, volume_threshold=None, max_bars=None):  
        """  
        动态更新参数  
        :param volume_threshold: 新的交易量阈值  
        :param max_bars: 新的最大 Bar 数量限制  
        """  
        if volume_threshold is not None:  
            self.volume_threshold = volume_threshold  
        if max_bars is not None:  
            self.max_bars = max_bars  
            # 如果当前 Bar 数量超过新的限制，移除最早的 Bar  
            if len(self.bars_df) > self.max_bars:  
                self.bars_df = self.bars_df.iloc[-self.max_bars:]  