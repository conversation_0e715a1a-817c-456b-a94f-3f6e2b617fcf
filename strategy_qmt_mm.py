#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QMT做市商策略 - 完全对齐qmt_mm.py
集成AS模型、网格层数控制、动态参数调整等功能
"""

from dataclasses import dataclass
from typing import Dict, Any, Optional, Tuple, List
import pandas as pd
import numpy as np
import sys
import os
import math

# 添加utils路径以导入AS模型
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))

from strategy_base import StrategyBase, StrategyConfig, MarketData, Signal, strategy_manager
MIN_PRICE_STEP = 0.01 

@dataclass
class QMTMMConfig(StrategyConfig):
    """QMT做市商策略配置"""
    # 基础参数
    enter_lot: int = 5000
    grid_spread: float = 0.006  # 0.6% 网格价差 (对齐qmt_mm.py)
    tp_spread: float = 0.004    # 0.4% 止盈价差 (对齐qmt_mm.py)
    sl_ratio: float = 2.0       # 2倍止损比例
    min_spread: float = MIN_PRICE_STEP * 1.5  # 最小价差
    max_holding_time_seconds: float = 3600.0  # 最大持仓时间（秒），超过则止损
    
    # AS模型参数
    risk_probility: float = 0.01
    risk_probility_buy: float = 0.05
    risk_probility_sell: float = 0.1
    intensity_window: int = 60  # 强度估计窗口(秒)
    
    # 网格参数
    max_grid_layers: int = 3
    grid_spreadnet_param: List[float] = None  # 网格价差参数
    grid_spreadqty_param: List[float] = None  # 网格数量参数
    
    # Entropy参数
    entropy_ratio_buy_threshold: float = 1.2
    entropy_ratio_sell_threshold: float = 0.8
    entropy_ema_alpha: float = 0.3
    ratio_ema_alpha: float = 0.2
    
    # 价格更新参数
    order_update_interval: int = 1000  # 毫秒
    spread_lifetime: int = 9000  # 价差生命周期(毫秒)
    
    # 其他参数
    max_position: int = 50000
    
    def __post_init__(self):
        if self.grid_spreadnet_param is None:
            self.grid_spreadnet_param = [1.0, 1.5, 2.0, 2.5]  # 对齐qmt_mm.py
        if self.grid_spreadqty_param is None:
            self.grid_spreadqty_param = [1.0, 1.0, 2.0, 2.0]  # 对齐qmt_mm.py


class EmpiricalIntensityEstimator:
    """
    限价单信息的容器 - 完整AS模型实现
    """
    class LimitOrderTracker:
        def __init__(self, start_ts: int, order_price: float):
            self.start_ts = start_ts
            self.order_price = order_price

    class Fill:
        def is_order_filled(self, filled_price: float, order_price: float) -> bool:
            raise NotImplementedError()

    class SellFill(Fill):
        def is_order_filled(self, filled_price: float, order_price: float) -> bool:
            return filled_price > order_price

    class BuyFill(Fill):
        def is_order_filled(self, filled_price: float, order_price: float) -> bool:
            return filled_price < order_price

    def __init__(self, spread: float, spread_direction: float, dt: int):
        """
        @param spread 与中间价的距离，买入限价单使用负号，卖出限价单使用正号
        @param spread_direction -1 表示卖出限价单，1 表示买入限
        """
        self.spread = spread
        self.dt = dt
        self.initializing = True
        self.last_price = float('nan')
        self.last_limit_order_inserted = 0
        self.live_trackers: List[EmpiricalIntensityEstimator.LimitOrderTracker] = []
        self.live_trackers_start_time_sum = 0
        self.finished_trackers: List[Tuple[int, int]] = []
        self.finished_trackers_wait_time_sum = 0
        self.fill_comp = self.SellFill() if spread_direction > 0 else self.BuyFill()

    def on_tick(self, ref_price: float, fill_price: float, ts: int, window_start: int):

        if self.initializing:
            self.initializing = False
            self.last_limit_order_inserted = ts - self.dt

        while self.last_limit_order_inserted + self.dt < ts:
            self.last_limit_order_inserted += self.dt
            self.live_trackers.append(
                self.LimitOrderTracker(self.last_limit_order_inserted, self.last_price + self.spread)
            )
            self.live_trackers_start_time_sum += self.last_limit_order_inserted

        if self.last_limit_order_inserted + self.dt == ts:
            self.last_limit_order_inserted = ts
            self.live_trackers.append(self.LimitOrderTracker(ts, ref_price + self.spread))
            self.live_trackers_start_time_sum += ts

        self.last_price = ref_price

        for tracker in self.live_trackers[:]:
            if window_start > tracker.start_ts:
                self.live_trackers.remove(tracker)
                self.live_trackers_start_time_sum -= tracker.start_ts
                continue

            if self.fill_comp.is_order_filled(fill_price, tracker.order_price):
                self.live_trackers.remove(tracker)
                self.live_trackers_start_time_sum -= tracker.start_ts
                duration = ts - tracker.start_ts
                self.finished_trackers.append((tracker.start_ts, duration))
                self.finished_trackers_wait_time_sum += duration

    def estimate_intensity(self, ts: int, window_start: int) -> float:

        for tracker in self.finished_trackers[:]:
            if tracker[0] < window_start:
                self.finished_trackers.remove(tracker)
                self.finished_trackers_wait_time_sum -= tracker[1]

        if self.live_trackers and ts != self.live_trackers[-1].start_ts:
            for tracker in self.live_trackers[:]:
                if window_start > tracker.start_ts:
                    self.live_trackers.remove(tracker)
                    self.live_trackers_start_time_sum -= tracker.start_ts

        if not self.live_trackers:
            return 0.0

        numerator = self.dt * len(self.finished_trackers)
        denominator = (
            len(self.live_trackers) * ts
            - self.live_trackers_start_time_sum
            + self.finished_trackers_wait_time_sum
        )

        if denominator == 0:
            return 0.0
        return max(float(numerator) / denominator, 1e-6)


class AbstractAkSolver:
    def __init__(self, spread_specification: np.ndarray):
        self.spread_specification = np.abs(spread_specification)

    def solve_ak(self, intensities: np.ndarray) -> np.ndarray:
        raise NotImplementedError("子类必须实现此方法")


class AkMultiCurveSolver(AbstractAkSolver):
    def __init__(self, spread_specification: np.ndarray):
        """
        近似求解A k
        """
        super().__init__(spread_specification)
        n_estimates = len(spread_specification) * (len(spread_specification) - 1) // 2
        self.k_estimates = np.zeros(n_estimates)
        self.a_estimates = np.zeros(n_estimates)

    def solve_ak(self, intensities: np.ndarray) -> np.ndarray:
        est_idx = 0
        with np.errstate(divide='ignore', invalid='ignore'):
            for i in range(len(intensities) - 1):
                for j in range(i + 1, len(intensities)):

                    self.k_estimates[est_idx] = (
                        np.log(intensities[j] / intensities[i]) /
                        (self.spread_specification[i] - self.spread_specification[j])
                    )
                    self.a_estimates[est_idx] = (
                        intensities[i] * np.exp(self.k_estimates[est_idx] * self.spread_specification[i])
                    )
                    est_idx += 1

        return np.array([np.mean(self.a_estimates), np.mean(self.k_estimates)])


class SpreadIntensityCurve:
    def __init__(self, spread_step: float, n_spreads: int, dt: int):
        # 这里 dt = 3000ms 了 由于 qmt本身的限制
        self.intensity_estimators: List[EmpiricalIntensityEstimator] = []
        spread_specification = np.zeros(n_spreads)
        self.intensity_estimates = np.zeros(n_spreads)

        for i in range(n_spreads):
            spread_specification[i] = i * spread_step
            self.intensity_estimators.append(
                EmpiricalIntensityEstimator(spread_specification[i], np.sign(spread_step), dt)
            )
        self.solver = AkMultiCurveSolver(spread_specification)

    def on_tick(self, ref_price: float, fill_price: float, ts: int, window_start: int):
        for estimator in self.intensity_estimators:
            estimator.on_tick(ref_price, fill_price, ts, window_start)

    def estimate_ak(self, ts: int, window_start: int) -> np.ndarray:
        for i, estimator in enumerate(self.intensity_estimators):
            self.intensity_estimates[i] = estimator.estimate_intensity(ts, window_start)
        spread_specification = np.array([estimator.spread for estimator in self.intensity_estimators])
        return self.solver.solve_ak(self.intensity_estimates)


def get_spread(target_intensity: float, a: float, k: float) -> float:
    try:
        return -math.log(target_intensity / a) / k
    except Exception as e:
        print(f"Error in get_spread: {e}")
        return 5

def dynamic_mid(ask_price: float, bid_price: float, ask_vol: float, bid_vol: float) -> float:
    """动态中间价计算"""
    if ask_vol + bid_vol == 0:
        return (ask_price + bid_price) / 2
    
    # 基于成交量加权的中间价
    total_vol = ask_vol + bid_vol
    return (bid_price * ask_vol + ask_price * bid_vol) / total_vol


def max_vol_spread(tick_data) -> float:
    """基于最大成交量的价差 - 完全对齐qmt_mm_stk.py"""
    try:
        # 获取bid volumes，如果是MarketData对象
        if hasattr(tick_data, 'bid_volumes') and tick_data.bid_volumes:
            bid_volumes = tick_data.bid_volumes
            bid_prices = getattr(tick_data, 'bid_prices', [])
            last_price = tick_data.last_price

            if bid_volumes and bid_prices and len(bid_volumes) > 0 and len(bid_prices) > 0:
                max_value = max(bid_volumes)
                max_index = bid_volumes.index(max_value)
                if max_index < len(bid_prices):
                    return last_price - bid_prices[max_index]

        # 如果是字典格式（类似qmt_mm_stk.py中的tick）
        elif isinstance(tick_data, dict):
            if 'bidVol' in tick_data and 'bidPrice' in tick_data and 'lastPrice' in tick_data:
                max_value = max(tick_data['bidVol'])
                max_index = tick_data['bidVol'].index(max_value)
                return tick_data['lastPrice'] - tick_data['bidPrice'][max_index]

        # 默认返回值
        return 0.003
    except Exception as e:
        print(f"max_vol_spread计算错误: {e}")
        return 0.003

def factor_entropy(volumes: List[float], level: int = 5) -> float:
    """计算熵因子 - 对齐qmt_mm_stk.py"""
    if not volumes or len(volumes) == 0:
        return 0.0

    total_vol = sum(volumes[:level])
    if total_vol == 0:
        return 0.0

    entropy = 0
    for i in range(min(level, len(volumes))):
        if volumes[i] > 0:
            prob = volumes[i] / total_vol
            entropy -= prob * math.log(prob)
    return entropy

def shortly_momentum(ap_t1: float, bp_t1: float, ap_n: float, bp_n: float) -> Tuple[str, float]:
    """
    短期动能计算 - 对齐qmt_mm_stk.py
    @param ap_t1: 上一时刻ask价格
    @param bp_t1: 上一时刻bid价格
    @param ap_n: 当前ask价格
    @param bp_n: 当前bid价格
    """
    signal = 'hold'
    momentum = 0.0
    if bp_t1 > ap_n:
        return 'sell', 0.75
    elif ap_t1 < bp_n:
        return 'buy', 0.75
    elif ap_t1 < ap_n and bp_t1 < bp_n:
        return 'buy', 0.5
    elif ap_t1 > ap_t1 and bp_t1 > bp_n:  # 注意：这里可能是原代码的bug，但保持一致
        return 'sell', 0.5

    return signal, momentum


def update_entropy_metrics(bid_entropy: float, ask_entropy: float, strategy_instance) -> float:
    """
    更新entropy相关指标：比值和EMA - 对齐qmt_mm_stk.py
    """
    # 计算ask/bid entropy比值（修正方向）
    # 比值 > 1: ask分散/bid集中 = 买盘强势
    # 比值 < 1: ask集中/bid分散 = 卖盘强势
    if bid_entropy > 0.001:
        current_entropy_ratio = ask_entropy / bid_entropy
    else:
        current_entropy_ratio = 1.0

    if strategy_instance.bid_entropy_ema == 0:
        strategy_instance.bid_entropy_ema = bid_entropy
        strategy_instance.ask_entropy_ema = ask_entropy
        strategy_instance.entropy_ratio_ema = current_entropy_ratio
    else:
        alpha = strategy_instance.config.entropy_ema_alpha
        ratio_alpha = strategy_instance.config.ratio_ema_alpha
        strategy_instance.bid_entropy_ema = strategy_instance.bid_entropy_ema * (1 - alpha) + bid_entropy * alpha
        strategy_instance.ask_entropy_ema = strategy_instance.ask_entropy_ema * (1 - alpha) + ask_entropy * alpha
        strategy_instance.entropy_ratio_ema = strategy_instance.entropy_ratio_ema * (1 - ratio_alpha) + current_entropy_ratio * ratio_alpha

    # 更新当前比值
    strategy_instance.entropy_ratio = current_entropy_ratio

    return current_entropy_ratio


def round_price(price: float) -> float:
    """将价格精确到0.001"""
    return round(price * 1000) / 1000


class QMTMMStrategy(StrategyBase):
    """QMT做市商策略 - 完全对齐qmt_mm.py"""

    def __init__(self, config: QMTMMConfig):
        super().__init__(config)
        
        # 策略状态
        self.position_quantity = 0
        self.position_avg_price = 0.0
        self.internal_cost = -1.0  # 内部维护的成本价
        self.enter_price = 0.0
        
        # 网格状态
        self.grid_add_layer = 0
        self.reach_tp1 = False
        self.if_add_vol = False
        self.sell_enable = False
        self.position_start_time = None  # 持仓开始时间（毫秒）
        
        # 价格状态
        self.buy_price = 999.0
        self.sell_price = 0.0
        self.last_bid_spread = 0.003
        self.last_ask_spread = 0.003
        self.last_valid_time = 0
        self.last_order_update_time = 0
        
        # Entropy状态
        self.ask_entropy = 0.0
        self.bid_entropy = 0.0
        self.entropy_ratio = 1.0
        self.entropy_ratio_ema = 1.0
        self.bid_entropy_ema = 0.0
        self.ask_entropy_ema = 0.0

        # 短期动能状态
        self.last_askp1 = 0.0
        self.last_bidp1 = 0.0
        
        # AS模型强度估计器 - 使用完整的SpreadIntensityCurve
        self.buy_est = SpreadIntensityCurve(spread_step=-MIN_PRICE_STEP, n_spreads=5, dt=1000)
        self.sell_est = SpreadIntensityCurve(spread_step=MIN_PRICE_STEP, n_spreads=5, dt=1000)
        
        print("🤖 QMT做市商策略初始化完成")
    
    def initialize(self, historical_data: pd.DataFrame):
        """策略初始化"""
        print(f"🔄 QMT做市商策略初始化...")
        print(f"📊 历史数据: {len(historical_data)} 行")
        
        # 初始化强度估计器
        if len(historical_data) > 0:
            for _, row in historical_data.tail(50).iterrows():  # 使用最近50行初始化
                timestamp = int(row.get('timestamp', 0))
                mid_price = dynamic_mid(row.get('ask_price', 0), row.get('bid_price', 0),
                                     row.get('ask_volume', 0), row.get('bid_volume', 0))

                self.buy_est.on_tick(mid_price, row.get('bid_price', 0), timestamp, timestamp - 60000)
                self.sell_est.on_tick(mid_price, row.get('ask_price', 0), timestamp, timestamp - 60000)
        
        self.initialized = True
        print(f"✅ QMT做市商策略初始化完成")
    
    def calculate_entropy(self, data: MarketData, side: str) -> float:
        """计算熵因子"""
        if not hasattr(data, 'bid_volumes') or not hasattr(data, 'ask_volumes'):
            return 0.0
        
        if side == 'bid' and data.bid_volumes:
            volumes = np.array(data.bid_volumes[:5])  # 前5档
        elif side == 'ask' and data.ask_volumes:
            volumes = np.array(data.ask_volumes[:5])  # 前5档
        else:
            return 0.0
        
        # 计算熵
        volumes = volumes[volumes > 0]  # 过滤零成交量
        if len(volumes) == 0:
            return 0.0
        
        probs = volumes / np.sum(volumes)
        entropy = -np.sum(probs * np.log(probs + 1e-10))
        return entropy
    
    def update_entropy(self, data: MarketData):
        """更新熵指标 - 对齐qmt_mm_stk.py"""
        # 使用factor_entropy函数计算熵
        ask_volumes = getattr(data, 'ask_volumes', []) or []
        bid_volumes = getattr(data, 'bid_volumes', []) or []

        ask_entropy = factor_entropy(ask_volumes)
        bid_entropy = factor_entropy(bid_volumes)

        # 使用对齐的entropy更新函数
        entropy_rate = update_entropy_metrics(bid_entropy, ask_entropy, self)

        print(f"ask entropy: {ask_entropy:.4f}, bid entropy: {bid_entropy:.4f}, entropy ratio: {entropy_rate:.4f}")

        self.ask_entropy = ask_entropy
        self.bid_entropy = bid_entropy
    
    def get_entropy_trading_signal(self) -> Tuple[str, float]:
        """获取熵交易信号 - 对齐qmt_mm_stk.py"""
        if self.bid_entropy_ema == 0 or self.ask_entropy_ema == 0:
            return 'hold', 0.0

        if self.entropy_ratio_ema == 0:
            return 'hold', 0.0

        # 基于当前比值的信号
        if self.ask_entropy/self.bid_entropy > self.config.entropy_ratio_buy_threshold:
            # ask entropy高，卖盘分散，买盘集中，倾向买入
            signal = 'buy'
            strength = min((self.ask_entropy/self.bid_entropy - 1.0) / 0.5, 1.0)  # 标准化到0-1
        elif self.ask_entropy/self.bid_entropy < self.config.entropy_ratio_sell_threshold:
            # bid entropy高，买盘分散，卖盘集中，倾向卖出
            signal = 'sell'
            strength = min((1.0 - self.ask_entropy/self.bid_entropy) / 0.5, 1.0)  # 标准化到0-1
        else:
            signal = 'hold'
            strength = 0.0

        # 基于entropy_ratio突破entropy_ratio_ema的增强逻辑
        ema_trend_factor = 1.0
        entropy_ema_spread_factor = 1.1  # 从config中获取或使用默认值

        if self.bid_entropy / self.bid_entropy_ema > entropy_ema_spread_factor:
            if signal == 'hold':
                signal = 'sell'
                strength = 0.2
            if signal == 'buy':
                signal = 'hold'
                strength = 0.0
            if signal == 'sell':
                strength = min(strength * 1.2, 1.0)
        elif self.ask_entropy / self.ask_entropy_ema > entropy_ema_spread_factor:
            if signal == 'hold':
                signal = 'buy'
                strength = 0.2

        final_strength = min(strength * ema_trend_factor, 1.0)

        return signal, final_strength
    
    def should_update_prices(self, timestamp: int) -> bool:
        """判断是否应该更新价格"""
        return timestamp - self.last_order_update_time > self.config.order_update_interval
    
    def update_prices(self, data: MarketData):
        """更新买卖价格 - 集成AS模型"""
        # 检查价格数据有效性
        if pd.isna(data.ask_price) or pd.isna(data.bid_price) or pd.isna(data.ask_volume) or pd.isna(data.bid_volume):
            return  # 跳过无效数据

        mid_price = dynamic_mid(data.ask_price, data.bid_price, data.ask_volume, data.bid_volume)
        
        # 更新强度估计器
        self.buy_est.on_tick(mid_price, data.bid_price, data.timestamp, 
                           data.timestamp - self.config.intensity_window * 1000)
        self.sell_est.on_tick(mid_price, data.ask_price, data.timestamp,
                            data.timestamp - self.config.intensity_window * 1000)
        
        # 估计AS参数 - 完全对齐qmt_mm_stk.py
        buy_ak = self.buy_est.estimate_ak(data.timestamp,
                                        data.timestamp - self.config.intensity_window * 1000)
        sell_ak = self.sell_est.estimate_ak(data.timestamp,
                                          data.timestamp - self.config.intensity_window * 1000)

        # 计算价差 - 对齐qmt_mm_stk.py逻辑
        bid_spread = 0.0
        ask_spread = 0.0

        # 买入价差计算
        if not np.any(np.isnan(buy_ak)):
            if buy_ak[0] * buy_ak[1] != 0:
                filtered_bid_intensity = self.buy_est.intensity_estimates[self.buy_est.intensity_estimates > 1e-6]
                bid_spread = get_spread(self.config.risk_probility, buy_ak[0], buy_ak[1])
                bid_spread = max(bid_spread, 0.01)
                print(f"AS参数: buy_ak={buy_ak}, sell_ak={sell_ak}, filtered_intensity={len(filtered_bid_intensity)}")

        # 卖出价差计算
        if not np.any(np.isnan(sell_ak)):
            if sell_ak[0] * sell_ak[1] != 0:
                filtered_ask_intensity = self.sell_est.intensity_estimates[self.sell_est.intensity_estimates > 1e-6]
                if len(filtered_ask_intensity) and filtered_ask_intensity[-1] * sell_ak[0] > 0:
                    if len(filtered_ask_intensity) < 4:
                        ask_spread = get_spread(0.01, sell_ak[0], sell_ak[1])
                    else:
                        ask_spread = get_spread(0.01, sell_ak[0], sell_ak[1])

        # 价差生命周期检查 - 对齐qmt_mm_stk.py
        spread_lifetime = 9000
        maxvol_bid_spread = max_vol_spread(data)
        # if data.timestamp - self.last_valid_time > spread_lifetime and bid_spread <= 0:
        #     if bid_spread > maxvol_bid_spread:
        #         bid_spread = maxvol_bid_spread

        # 短期动能计算 - 对齐qmt_mm_stk.py
        direction, scores = shortly_momentum(self.last_askp1, self.last_bidp1, data.ask_price, data.bid_price)
        print(f'短期动能: {direction}, 强度: {scores}')

        # 根据熵信号和短期动能调整价差 - 对齐qmt_mm_stk.py
        market_type, _ = self.get_entropy_trading_signal()
        if data.timestamp - self.last_order_update_time > self.config.order_update_interval or \
                market_type != "hold" or direction != "hold":
            if not np.any(np.isnan(buy_ak)):
                if buy_ak[0] * buy_ak[1] != 0:
                    if market_type == "sell":
                        bid_spread = get_spread(self.config.risk_probility / 2, buy_ak[0], buy_ak[1])
                    elif market_type == "buy":
                        bid_spread = get_spread(self.config.risk_probility * 2, buy_ak[0], buy_ak[1])
            else:
                return  # 如果没有有效的AS参数，跳过价格更新

            # 短期动能对价差的影响
            if direction == "sell":
                bid_spread *= 1.2
            if market_type == "sell":
                bid_spread *= 1.1

            # 更新买入价格
            self.buy_price = round((mid_price - bid_spread) * 100) / 100
            if self.buy_price > data.bid_price and direction == "sell":
                self.buy_price = round((data.bid_price - bid_spread) * 100) / 100

            self.sell_price = round((data.last_price + ask_spread) * 100) / 100
            self.last_order_update_time = data.timestamp

            print(f"中间价: {mid_price:.3f}, 买1价: {data.bid_price:.3f}")
            print(f'价格更新: bid挂单价格: {self.buy_price:.3f}, bid spread: {bid_spread:.4f}, max bidvol spread: {maxvol_bid_spread:.4f}')

        # 更新状态
        self.last_bid_spread = bid_spread
        self.last_ask_spread = ask_spread
        self.last_askp1 = data.ask_price
        self.last_bidp1 = data.bid_price

    def on_market_data(self, data: MarketData) -> Optional[Signal]:
        """处理市场数据 - 完全对齐qmt_mm.py逻辑"""
        if not self.initialized:
            return None

        # 更新entropy指标
        self.update_entropy(data)

        # 更新价格 - 集成短期动能
        self.update_prices(data)

        # 生成交易信号
        return self.generate_signal(data)

    def generate_signal(self, data: MarketData) -> Optional[Signal]:
        """生成交易信号 - 完全对齐qmt_mm.py逻辑"""

        # 0. 14:55清仓检查 (优先级最高)
        if self.position_quantity != 0:
            close_signal = self._check_daily_close(data)
            if close_signal:
                return close_signal

        # 0.5. 持仓时间止损检查 (优先级第二高)
        if self.position_quantity != 0:
            time_stop_signal = self._check_holding_time_stop(data)
            if time_stop_signal:
                return time_stop_signal

        # 1. 止损优先 (sl first)
        if self.position_quantity > 0:
            sl_rate = self.config.sl_ratio  # 可以根据高波动调整
            loss_amount = (self.position_avg_price - data.last_price) * self.position_quantity
            sl_base = self.config.tp_spread * self.config.enter_lot

            if sl_base > 0 and (loss_amount / sl_base) > sl_rate:
                return Signal(
                    action='sell',
                    price=round_price(data.bid_price),
                    quantity=self.position_quantity,
                    reason="止损"
                )

        # 2. 开仓逻辑
        if not self.sell_enable:  # 没有持仓时
            # 如果价格还未更新（buy_price过高），使用市场价格开仓
            if self.buy_price > 100 or data.ask_price <= self.buy_price:
                buy_price = data.ask_price if self.buy_price > 100 else self.buy_price
                return Signal(
                    action='buy',
                    price=round_price(buy_price),
                    quantity=self.config.enter_lot,
                    reason="初始买入"
                )

        # 3. 持仓时的加仓和止盈逻辑
        else:
            exit_price = self.position_avg_price + self.config.tp_spread - 0.0001

            # 网格加仓逻辑
            if self.if_add_vol and self.grid_add_layer < self.config.max_grid_layers:
                reenter_price = self.enter_price
                reenter_lot = self.config.enter_lot

                for i in range(self.grid_add_layer + 1):
                    reenter_price -= self.config.grid_spread * self.config.grid_spreadnet_param[i]
                    reenter_lot = int(self.config.enter_lot * self.config.grid_spreadqty_param[i])

                if data.ask_price <= reenter_price:
                    return Signal(
                        action='buy',
                        price=round_price(reenter_price),
                        quantity=reenter_lot,
                        reason=f"网格加仓第{self.grid_add_layer + 1}层"
                    )

            # 止盈逻辑
            if self.if_add_vol:  # 有加仓的情况
                # 第一次止盈
                if data.bid_price >= exit_price and not self.reach_tp1:
                    tp1_quantity = max(100, (self.position_quantity + 100) // 200 * 100)
                    return Signal(
                        action='sell',
                        price=round_price(data.bid_price),
                        quantity=tp1_quantity,
                        reason="第一次止盈"
                    )

                # 第二次止盈
                elif data.bid_price >= exit_price + self.config.tp_spread and self.reach_tp1:
                    return Signal(
                        action='sell',
                        price=round_price(data.bid_price),
                        quantity=self.position_quantity,
                        reason="第二次止盈"
                    )

                # 继续加仓条件
                elif data.ask_price < self.position_avg_price - self.config.grid_spread:
                    if self.grid_add_layer < self.config.max_grid_layers:
                        reenter_price = self.enter_price
                        reenter_lot = self.config.enter_lot

                        for i in range(self.grid_add_layer + 1):
                            reenter_price -= self.config.grid_spread * self.config.grid_spreadnet_param[i]
                            reenter_lot = int(self.config.enter_lot * self.config.grid_spreadqty_param[i])

                        return Signal(
                            action='buy',
                            price=round_price(reenter_price),
                            quantity=reenter_lot,
                            reason=f"继续加仓第{self.grid_add_layer + 1}层"
                        )

            else:  # 简单止盈 (无加仓状态)
                if data.bid_price >= exit_price:
                    return Signal(
                        action='sell',
                        price=round_price(data.bid_price),
                        quantity=self.position_quantity,
                        reason="简单止盈"
                    )
                # 加仓条件 - 即使在简单模式下也可以加仓
                elif data.ask_price < self.position_avg_price - self.config.grid_spread:
                    if self.grid_add_layer < self.config.max_grid_layers:
                        reenter_price = self.enter_price
                        reenter_lot = self.config.enter_lot

                        for i in range(self.grid_add_layer + 1):
                            reenter_price -= self.config.grid_spread * self.config.grid_spreadnet_param[i]
                            reenter_lot = int(self.config.enter_lot * self.config.grid_spreadqty_param[i])

                        return Signal(
                            action='buy',
                            price=round_price(reenter_price),
                            quantity=reenter_lot,
                            reason=f"初次加仓第{self.grid_add_layer + 1}层"
                        )

        return None

    def on_trade_executed(self, price: float, quantity: int, side: str, timestamp: int) -> None:
        """交易执行回调 - 完全对齐qmt_mm.py逻辑"""
        if side == 'buy':
            # 买入逻辑
            # 记录首次开仓时间
            if self.position_quantity == 0:
                self.position_start_time = timestamp
            if self.position_quantity == 0:
                # 初始买入 - 注意：if_add_vol保持False，只有实际加仓时才设为True
                self.internal_cost = price
                self.enter_price = price
                self.position_quantity = quantity
                self.position_avg_price = price
                self.sell_enable = True
                # self.if_add_vol = False  # 初始买入时保持False
                print(f"初始买入: {quantity}手，价格: {price:.6f}")
            else:
                # 加仓 - 实际加仓时设置if_add_vol = True
                if self.internal_cost > 0:
                    self.internal_cost = (self.position_quantity * self.internal_cost + quantity * price) / (self.position_quantity + quantity)

                self.position_quantity += quantity
                self.position_avg_price = self.internal_cost
                self.grid_add_layer += 1
                self.if_add_vol = True  # 实际加仓时才设为True
                print(f"加仓: {quantity}手，价格: {price:.6f}，当前均价: {self.internal_cost:.6f}")

        elif side == 'sell':
            # 卖出逻辑
            if quantity == self.position_quantity:
                # 全部卖出
                self.position_quantity = 0
                self.position_avg_price = 0
                self.internal_cost = -1.0
                self.enter_price = 0
                self.sell_enable = False
                self.if_add_vol = False
                self.reach_tp1 = False
                self.grid_add_layer = 0
                self.position_start_time = None  # 清除持仓开始时间
                print(f"全部卖出: {quantity}手，价格: {price:.6f}")
            else:
                # 部分卖出（第一次止盈）
                self.position_quantity -= quantity
                self.reach_tp1 = True
                self.grid_add_layer //= 2  # 减少网格层数
                print(f"部分卖出: {quantity}手，价格: {price:.6f}，剩余: {self.position_quantity}手")

    def get_optimization_params(self) -> Dict[str, Tuple[float, float]]:
        """获取优化参数范围"""
        return {
            'enter_lot': (10000, 10000),        # 每次交易数量
            'grid_spread': (0.001, 0.01),    # 0.05%-0.2% 网格价差
            'tp_spread': (0.001, 0.01),      # 0.02%-0.1% 止盈价差
            'sl_ratio': (1, 15.0),            # 1.5-4倍止损比例
            'risk_probility': (0.0008, 0.1),   # AS模型风险概率
            'entropy_ratio_buy_threshold': (1.05, 1.5),
            'entropy_ratio_sell_threshold': (0.5, 0.95),
            'order_update_interval': (3000, 30000),  # 价格更新间隔(毫秒)
            'min_spread': (0.000, 0.002),      # 最小价差
            'max_holding_time_seconds': (300, 5400)  # 30分钟-2小时最大持仓时间
        }

    def update_params(self, params: Dict[str, Any]) -> None:
        """更新策略参数"""
        for key, value in params.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                print(f"🔧 更新参数: {key} = {value}")

    def _check_daily_close(self, data: MarketData) -> Optional[Signal]:
        """检查每日14:55清仓"""
        from datetime import datetime

        # 将毫秒时间戳转换为datetime
        dt = datetime.fromtimestamp(data.timestamp / 1000)

        # 检查是否为交易日的14:55之后到15:00之前
        if (dt.hour == 14 and dt.minute >= 55) or (dt.hour == 15 and dt.minute < 1):
            # 强制清仓
            if self.position_quantity > 0:
                return Signal(
                    action="sell",
                    quantity=self.position_quantity,
                    price=round_price(data.bid_price),
                    reason="Daily close at 14:55 - Long position"
                )
            elif self.position_quantity < 0:
                return Signal(
                    action="buy",
                    quantity=abs(self.position_quantity),
                    price=round_price(data.ask_price),
                    reason="Daily close at 14:55 - Short position"
                )

        return None

    def _check_holding_time_stop(self, data: MarketData) -> Optional[Signal]:
        """检查持仓时间止损"""
        if self.position_start_time is None:
            return None

        # 计算持仓时间（秒）
        holding_time_seconds = (data.timestamp - self.position_start_time) / 1000

        # 检查是否超过最大持仓时间
        if holding_time_seconds > self.config.max_holding_time_seconds:
            # 强制止损
            if self.position_quantity > 0:
                return Signal(
                    action="sell",
                    quantity=self.position_quantity,
                    price=round_price(data.bid_price),
                    reason=f"Holding time stop loss: {holding_time_seconds:.1f}s > {self.config.max_holding_time_seconds:.1f}s"
                )
            elif self.position_quantity < 0:
                return Signal(
                    action="buy",
                    quantity=abs(self.position_quantity),
                    price=round_price(data.ask_price),
                    reason=f"Holding time stop loss: {holding_time_seconds:.1f}s > {self.config.max_holding_time_seconds:.1f}s"
                )

        return None


# 注册QMT策略
strategy_manager.register_strategy('qmt_mm', QMTMMStrategy, QMTMMConfig)
