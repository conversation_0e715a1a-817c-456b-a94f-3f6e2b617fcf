#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查超长持仓交易的时间
"""

import json
from datetime import datetime, timed<PERSON><PERSON>

def check_long_trades():
    """检查超长持仓交易"""
    print("🔍 检查超长持仓交易")
    print("=" * 50)
    
    # 加载交易数据
    with open('optimization_results/optimization_results_qmt_mm.json', 'r') as f:
        data = json.load(f)
    
    trades = data['train_results']['trades']
    
    # 找出持仓时间超过1天的交易
    long_trades = []
    for trade in trades:
        if trade.get('holding_time', 0) > 24 * 3600 * 1000:  # 超过24小时
            long_trades.append(trade)
    
    print(f"📊 总交易数: {len(trades)}")
    print(f"📊 超过24小时的持仓: {len(long_trades)} 笔")
    
    if not long_trades:
        print("✅ 没有超长持仓交易")
        return
    
    print(f"\n🔍 超长持仓交易分析:")
    print("=" * 60)
    
    for i, trade in enumerate(long_trades[:5]):  # 显示前5笔
        entry_time = datetime.fromtimestamp(trade['entry_timestamp'] / 1000)
        exit_time = datetime.fromtimestamp(trade['timestamp'] / 1000)
        holding_hours = trade['holding_time'] / (1000 * 3600)
        
        print(f"\n交易 #{i+1}:")
        print(f"  开仓时间: {entry_time.strftime('%Y-%m-%d %H:%M:%S')} ({entry_time.strftime('%A')})")
        print(f"  平仓时间: {exit_time.strftime('%Y-%m-%d %H:%M:%S')} ({exit_time.strftime('%A')})")
        print(f"  持仓时间: {holding_hours:.1f} 小时")
        print(f"  平仓原因: {trade['reason']}")
        
        # 检查是否跨越了周末
        days_diff = (exit_time.date() - entry_time.date()).days
        if days_diff > 0:
            print(f"  跨越天数: {days_diff} 天")
            
            # 检查开仓和平仓时间
            entry_hour = entry_time.hour
            exit_hour = exit_time.hour
            
            print(f"  开仓时刻: {entry_hour:02d}:{entry_time.minute:02d}")
            print(f"  平仓时刻: {exit_hour:02d}:{exit_time.minute:02d}")
            
            # 检查是否在交易时间内
            if entry_hour >= 15 or entry_hour < 9:
                print(f"  ⚠️ 开仓时间在非交易时间")
            
            if exit_hour >= 15 or exit_hour < 9:
                print(f"  ⚠️ 平仓时间在非交易时间")
            
            # 检查是否包含周末
            current_date = entry_time.date()
            weekend_days = []
            while current_date <= exit_time.date():
                weekday = current_date.weekday()  # 0=Monday, 6=Sunday
                if weekday >= 5:  # Saturday=5, Sunday=6
                    weekend_days.append(current_date.strftime('%Y-%m-%d %A'))
                current_date += timedelta(days=1)
            
            if weekend_days:
                print(f"  ⚠️ 包含周末: {len(weekend_days)} 天")
                for day in weekend_days[:2]:  # 显示前2个周末
                    print(f"    - {day}")
    
    # 分析14:55清盘逻辑为什么没有工作
    print(f"\n🔍 14:55清盘逻辑分析:")
    print("=" * 40)
    
    # 检查是否有在14:55-15:00之间的交易
    close_time_trades = []
    for trade in trades:
        exit_time = datetime.fromtimestamp(trade['timestamp'] / 1000)
        if (exit_time.hour == 14 and exit_time.minute >= 55) or (exit_time.hour == 15 and exit_time.minute < 1):
            close_time_trades.append(trade)
    
    print(f"📊 在14:55-15:00之间平仓的交易: {len(close_time_trades)} 笔")
    
    # 检查是否有"Daily close"原因的交易
    daily_close_trades = [t for t in trades if 'Daily close' in t.get('reason', '')]
    print(f"📊 'Daily close'原因的交易: {len(daily_close_trades)} 笔")
    
    if daily_close_trades:
        print(f"✅ 14:55清盘逻辑有在工作")
        for i, trade in enumerate(daily_close_trades[:3]):
            exit_time = datetime.fromtimestamp(trade['timestamp'] / 1000)
            print(f"  #{i+1}: {exit_time.strftime('%H:%M:%S')} - {trade['reason']}")
    else:
        print(f"⚠️ 没有发现'Daily close'交易，可能的原因:")
        print(f"  1. 数据中没有14:55-15:00的时间点")
        print(f"  2. 时区问题")
        print(f"  3. 清盘逻辑被其他条件覆盖")

if __name__ == "__main__":
    check_long_trades()
