#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主程序入口 - 策略回测和优化
"""

import argparse
import json
import importlib
import os
from strategy_base import strategy_manager
from optuna_optimizer import run_optimization, continue_optimization
from backtest_engine import BacktestConfig, BacktestEngine, load_data


def load_strategy_module(strategy_name: str) -> None:
    """动态加载策略模块"""
    try:
        module_name = f"strategy_{strategy_name}"
        importlib.import_module(module_name)
        print(f"✅ 策略模块 {module_name} 加载成功")
    except ImportError as e:
        print(f"❌ 策略模块 {module_name} 加载失败: {e}")
        raise


def main():
    parser = argparse.ArgumentParser(description='策略回测和优化系统')
    parser.add_argument('--strategy', type=str, help='策略名称')
    parser.add_argument('--data-dir', type=str, default='backtest_data', help='数据目录')
    parser.add_argument('--symbol', type=str, help='标的代码 (如: ETF_510300)')
    parser.add_argument('--trials', type=int, default=100, help='优化试验次数')
    parser.add_argument('--train-ratio', type=float, default=0.7, help='训练集比例')
    parser.add_argument('--list-strategies', action='store_true', help='列出所有可用策略')
    parser.add_argument('--list-symbols', action='store_true', help='列出所有可用标的')
    parser.add_argument('--backtest-only', action='store_true', help='只运行回测，不优化')
    parser.add_argument('--continue-study', type=str, help='继续现有的优化研究')
    parser.add_argument('--snapshot-only', action='store_true', default=True, help='只使用snapshot数据进行回测 (默认: True)')
    parser.add_argument('--all-data', action='store_true', help='使用所有数据进行回测 (包括trade数据)')

    args = parser.parse_args()

    if args.list_strategies:
        # 尝试加载所有策略模块
        for file in os.listdir('.'):
            if file.startswith('strategy_') and file.endswith('.py'):
                strategy_name = file[9:-3]  # 去掉 'strategy_' 前缀和 '.py' 后缀
                try:
                    load_strategy_module(strategy_name)
                except:
                    pass

        strategies = strategy_manager.list_strategies()
        print("📋 可用策略:")
        for strategy in strategies:
            print(f"  - {strategy}")
        return

    if args.list_symbols:
        # 列出所有可用标的
        try:
            from data_manager import DataManager
            manager = DataManager()
            manager.config.base_dir = args.data_dir
            symbols = manager.list_symbols()

            print("📊 可用标的:")
            for symbol in symbols:
                info = manager.get_symbol_info(symbol)
                metadata = info.get('metadata', {})
                print(f"  - {symbol}: {metadata.get('name', 'Unknown')} ({metadata.get('exchange', 'Unknown')})")
        except Exception as e:
            print(f"❌ 获取标的列表失败: {e}")
        return

    if not args.strategy:
        print("❌ 请指定策略名称 (--strategy) 或使用 --list-strategies 查看可用策略")
        return
    
    print(f"🚀 开始策略处理")
    print(f"📊 策略: {args.strategy}")
    print(f"📁 数据目录: {args.data_dir}")
    print("=" * 60)

    try:
        if args.continue_study:
            # 继续现有优化
            print(f"🔄 继续优化研究: {args.continue_study}")
            print(f"🔢 增加试验次数: {args.trials}")
            results = continue_optimization(
                strategy_name=args.strategy,
                study_name=args.continue_study,
                data_dir=args.data_dir,
                symbol=args.symbol,
                additional_trials=args.trials,
                train_ratio=args.train_ratio
            )
        elif args.backtest_only:
            # 只运行回测
            print(f"📊 只运行回测")
            if args.symbol:
                print(f"🎯 使用标的: {args.symbol}")

            # 确定是否只使用snapshot数据
            snapshot_only = not args.all_data  # 如果指定了--all-data，则不只使用snapshot
            if snapshot_only:
                print(f"📊 数据模式: 只使用snapshot数据")
            else:
                print(f"📊 数据模式: 使用所有数据 (包括trade)")

            load_strategy_module(args.strategy)
            data = load_data(args.data_dir, args.symbol, snapshot_only=snapshot_only)
            strategy = strategy_manager.create_strategy(args.strategy, {})
            config = BacktestConfig()
            config.snapshot_only = snapshot_only
            engine = BacktestEngine(config, strategy)
            results = {'backtest_results': engine.run_backtest(data)}
        else:
            # 运行优化
            print(f"🔢 试验次数: {args.trials}")
            print(f"📈 训练集比例: {args.train_ratio}")

            # 确定是否只使用snapshot数据
            snapshot_only = not args.all_data
            if snapshot_only:
                print(f"📊 数据模式: 只使用snapshot数据")
            else:
                print(f"📊 数据模式: 使用所有数据 (包括trade)")

            results = run_optimization(
                strategy_name=args.strategy,
                data_dir=args.data_dir,
                symbol=args.symbol,
                n_trials=args.trials,
                train_ratio=args.train_ratio
            )
        
        # 确保结果目录存在
        os.makedirs("optimization_results", exist_ok=True)

        # 保存结果
        if args.backtest_only:
            output_file = f"optimization_results/backtest_results_{args.strategy}.json"
            save_results = results['backtest_results']
        else:
            output_file = f"optimization_results/optimization_results_{args.strategy}.json"
            # 移除不能序列化的对象
            save_results = {
                'best_params': results['best_params'],
                'best_value': results['best_value'],
                'train_results': results['train_results'],
                'test_results': results['test_results']
            }

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(save_results, f, indent=2, ensure_ascii=False)

        print(f"✅ 结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"❌ 优化失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

#    python main.py --strategy qmt_mm --symbol ETF_513120 --trials 10
# python generate_complete_charts.py