from concurrent.futures import Future, Executor
from typing import Callable, Collection, List, TypeVar

T = TypeVar('T')

"""
提供对全局执行器服务的统一访问
"""
class EstimationExecutor:
    executor: Executor = None

    @staticmethod
    def set_executor(executor: Executor) -> None:
        """
        @param executor 将运行估计的全局执行器
        """
        EstimationExecutor.executor = executor

    @staticmethod
    def submit(c: Callable[[], T]) -> Future[T]:
        return EstimationExecutor.executor.submit(c)

    @staticmethod
    def invoke_all(cc: Collection[Callable[[], T]]) -> List[Future[T]]:
        return list(EstimationExecutor.executor.map(lambda c: c(), cc))
