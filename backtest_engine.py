#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
纯粹的回测引擎 - 只负责回测逻辑
"""

import pandas as pd
import numpy as np
from dataclasses import dataclass
from typing import Dict, Any, List, Optional, Tuple
import os

from strategy_base import StrategyBase, MarketData, Signal


@dataclass
class BacktestConfig:
    """回测配置"""
    initial_cash: float = 1000000.0
    commission_rate: float = 0.00005  # 万0.5手续费
    max_position: int = 500000
    snapshot_only: bool = True  # 只使用snapshot数据进行回测


@dataclass
class Position:
    """持仓信息"""
    quantity: int = 0
    avg_price: float = 0.0
    realized_pnl: float = 0.0
    unrealized_pnl: float = 0.0


@dataclass
class Trade:
    """交易记录"""
    timestamp: int
    price: float
    quantity: int
    side: str  # 'buy' or 'sell'
    commission: float
    pnl: float
    reason: str = ""
    holding_time: Optional[int] = None  # 持仓时间（秒）
    entry_timestamp: Optional[int] = None  # 开仓时间


class BacktestEngine:
    """纯粹的回测引擎"""
    
    def __init__(self, config: BacktestConfig, strategy: StrategyBase):
        self.config = config
        self.strategy = strategy

        # 交易状态
        self.cash = config.initial_cash
        self.position = Position()
        self.trades: List[Trade] = []
        self.equity_curve: List[Tuple[int, float]] = []

        # 统计信息
        self.total_commission = 0.0

        # 持仓时间跟踪
        self.position_entry_time = None  # 首次开仓时间
        self.position_history = []  # 持仓历史 [(timestamp, quantity, avg_price)]
    
    def reset(self):
        """重置回测状态"""
        self.cash = self.config.initial_cash
        self.position = Position()
        self.trades.clear()
        self.equity_curve.clear()
        self.total_commission = 0.0
        self.position_entry_time = None
        self.position_history.clear()
        self.strategy.reset()
    
    def convert_row_to_market_data(self, row: pd.Series) -> MarketData:
        """将数据行转换为MarketData对象"""
        # 提取5档深度数据
        bid_volumes = []
        ask_volumes = []
        bid_prices = []
        ask_prices = []
        
        for i in range(1, 6):
            if f'bid_vol_{i}' in row:
                vol_val = row.get(f'bid_vol_{i}', 0)
                if pd.notna(vol_val):
                    bid_volumes.append(float(vol_val))
            if f'ask_vol_{i}' in row:
                vol_val = row.get(f'ask_vol_{i}', 0)
                if pd.notna(vol_val):
                    ask_volumes.append(float(vol_val))
            if f'bid_price_{i}' in row:
                price_val = row.get(f'bid_price_{i}')
                if pd.notna(price_val):
                    bid_prices.append(float(price_val))
                elif pd.notna(row['bid_price']):
                    bid_prices.append(float(row['bid_price']))
            if f'ask_price_{i}' in row:
                price_val = row.get(f'ask_price_{i}')
                if pd.notna(price_val):
                    ask_prices.append(float(price_val))
                elif pd.notna(row['ask_price']):
                    ask_prices.append(float(row['ask_price']))
        
        # 处理可能的NaN值
        bid_price = row['bid_price'] if pd.notna(row['bid_price']) else (row.get('trade_price', 0) if pd.notna(row.get('trade_price')) else 0)
        ask_price = row['ask_price'] if pd.notna(row['ask_price']) else (row.get('trade_price', 0) if pd.notna(row.get('trade_price')) else 0)
        last_price = row['last_price'] if pd.notna(row['last_price']) else (row.get('trade_price', 0) if pd.notna(row.get('trade_price')) else 0)

        bid_vol = row.get('bid_vol_1', 0) if pd.notna(row.get('bid_vol_1')) else 0
        ask_vol = row.get('ask_vol_1', 0) if pd.notna(row.get('ask_vol_1')) else 0

        return MarketData(
            timestamp=int(row['timestamp']),
            bid_price=float(bid_price),
            ask_price=float(ask_price),
            last_price=float(last_price),
            bid_volume=float(bid_vol),
            ask_volume=float(ask_vol),
            bid_prices=bid_prices if bid_prices else None,
            ask_prices=ask_prices if ask_prices else None,
            bid_volumes=bid_volumes if bid_volumes else None,
            ask_volumes=ask_volumes if ask_volumes else None,
            extra_data={'event_type': row.get('event_type', 'snapshot')}
        )
    
    def execute_trade(self, signal: Signal, timestamp: int) -> bool:
        """执行交易"""
        if signal.quantity <= 0:
            return False
        
        # 检查资金和持仓限制
        if signal.action == 'buy':
            required_cash = signal.price * signal.quantity * (1 + self.config.commission_rate)
            if self.cash < required_cash:
                return False
            if self.position.quantity + signal.quantity > self.config.max_position:
                return False
        elif signal.action == 'sell':
            if self.position.quantity < signal.quantity:
                return False
        
        # 计算手续费
        commission = signal.price * signal.quantity * self.config.commission_rate

        # 计算持仓时间
        holding_time = None
        entry_timestamp = None

        # 执行交易
        if signal.action == 'buy':
            # 买入
            if self.position.quantity == 0:
                # 首次开仓
                self.position_entry_time = timestamp
                entry_timestamp = timestamp
            else:
                # 加仓
                entry_timestamp = self.position_entry_time

            total_cost = self.position.quantity * self.position.avg_price + signal.quantity * signal.price
            self.position.quantity += signal.quantity
            self.position.avg_price = total_cost / self.position.quantity if self.position.quantity > 0 else 0

            self.cash -= signal.price * signal.quantity + commission
            pnl = 0  # 买入开仓不计入盈亏，手续费在平仓时一并计算

        else:  # sell
            # 卖出
            if self.position_entry_time is not None:
                holding_time = timestamp - self.position_entry_time
                entry_timestamp = self.position_entry_time

            if self.position.avg_price > 0:
                gross_pnl = (signal.price - self.position.avg_price) * signal.quantity
                # 卖出时承担双倍手续费：买入时的手续费 + 卖出时的手续费
                total_commission = commission * 2  # 约等于买入+卖出的总手续费
                pnl = gross_pnl - total_commission
                self.position.realized_pnl += pnl
            else:
                pnl = -commission

            self.position.quantity -= signal.quantity
            if self.position.quantity <= 0:
                # 完全平仓
                self.position.quantity = 0
                self.position.avg_price = 0
                self.position_entry_time = None

            self.cash += signal.price * signal.quantity - commission
        
        # 记录持仓历史
        self.position_history.append((timestamp, self.position.quantity, self.position.avg_price))

        # 记录交易
        trade = Trade(
            timestamp=timestamp,
            price=signal.price,
            quantity=signal.quantity,
            side=signal.action,
            commission=commission,
            pnl=pnl,
            reason=signal.reason,
            holding_time=holding_time,
            entry_timestamp=entry_timestamp
        )
        self.trades.append(trade)
        self.total_commission += commission
        
        # 通知策略
        self.strategy.on_trade_executed(signal.price, signal.quantity, signal.action, timestamp)
        
        return True
    
    def get_total_equity(self, current_price: float) -> float:
        """计算总权益"""
        if self.position.quantity > 0 and self.position.avg_price > 0:
            # 持仓市值 = 当前价格 × 持仓数量
            position_value = current_price * self.position.quantity
            # 总权益 = 现金 + 持仓市值
            total_equity = self.cash + position_value

            # 计算未实现盈亏（用于记录，但不影响总权益计算）
            cost_basis = self.position.avg_price * self.position.quantity
            self.position.unrealized_pnl = position_value - cost_basis

            return total_equity
        else:
            self.position.unrealized_pnl = 0
            return self.cash
    
    def run_backtest(self, data: pd.DataFrame) -> Dict[str, Any]:
        """运行回测 - 核心方法"""
        self.reset()

        # 如果配置为只使用snapshot数据，进行过滤
        if self.config.snapshot_only and 'event_type' in data.columns:
            original_len = len(data)
            data = data[data['event_type'] == 'snapshot'].copy()
            print(f"📊 Snapshot过滤: {original_len} -> {len(data)} 行 ({len(data)/original_len*100:.1f}%)")

        # 策略初始化
        self.strategy.initialize(data.head(100))  # 用前100行数据初始化

        # 遍历数据
        for _, row in data.iterrows():
            market_data = self.convert_row_to_market_data(row)
            
            # 获取交易信号
            signal = self.strategy.on_market_data(market_data)
            
            # 执行交易
            if signal and signal.action in ['buy', 'sell']:
                self.execute_trade(signal, market_data.timestamp)
            
            # 记录权益曲线
            total_equity = self.get_total_equity(market_data.last_price)
            self.equity_curve.append((market_data.timestamp, total_equity))
        
        return self.calculate_results()
    
    def calculate_results(self) -> Dict[str, Any]:
        """计算回测结果 - 增强版本"""
        if len(self.equity_curve) == 0:
            return self._get_empty_results()

        # 基础数据
        initial_equity = self.config.initial_cash
        final_equity = self.equity_curve[-1][1]
        equity_values = [eq[1] for eq in self.equity_curve]

        # 1. 基础收益指标
        total_return = (final_equity - initial_equity) / initial_equity
        total_pnl = final_equity - initial_equity

        # 2. 风险指标
        max_drawdown = self._calculate_max_drawdown(equity_values)
        sharpe_ratio = self._calculate_sharpe_ratio(equity_values)
        calmar_ratio = self._calculate_calmar_ratio(total_return, max_drawdown)
        sortino_ratio = self._calculate_sortino_ratio()

        # 3. 交易统计
        trade_stats = self._calculate_trade_statistics()

        # 4. 持仓时间统计
        holding_time_stats = self._calculate_holding_time_statistics()

        # 5. PnL分析
        pnl_stats = self._calculate_pnl_statistics()

        # 合并所有结果
        results = {
            # 基础指标
            'total_return': total_return,
            'total_pnl': total_pnl,
            'final_equity': final_equity,
            'total_commission': self.total_commission,

            # 风险指标
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'calmar_ratio': calmar_ratio,
            'sortino_ratio': sortino_ratio,

            # 交易统计
            **trade_stats,

            # 持仓时间统计
            **holding_time_stats,

            # PnL分析
            **pnl_stats,

            # 画图数据
            'trades': self._serialize_trades(),
            'equity_curve': self.equity_curve,
            'position_history': self.position_history,
        }

        return results

    def _serialize_trades(self) -> List[Dict[str, Any]]:
        """序列化交易数据用于画图"""
        serialized_trades = []
        for trade in self.trades:
            serialized_trades.append({
                'timestamp': trade.timestamp,
                'price': trade.price,
                'quantity': trade.quantity,
                'side': trade.side,
                'commission': trade.commission,
                'pnl': trade.pnl,
                'reason': trade.reason,
                'holding_time': trade.holding_time,
                'entry_timestamp': trade.entry_timestamp
            })
        return serialized_trades

    def _get_empty_results(self) -> Dict[str, Any]:
        """返回空结果"""
        return {
            'total_return': 0.0,
            'total_pnl': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown': 0.0,
            'calmar_ratio': 0.0,
            'total_trades': 0,
            'win_rate': 0.0,
            'profit_loss_ratio': 0.0,
            'total_commission': 0.0,
            'final_equity': self.config.initial_cash,
            'profitable_trades': 0,
            'losing_trades': 0,
            'avg_holding_time_seconds': 0.0,
            'max_holding_time_seconds': 0.0,
            'min_holding_time_seconds': 0.0,
            'avg_profit_per_trade': 0.0,
            'max_profit_per_trade': 0.0,
            'max_loss_per_trade': 0.0,
            'profit_factor': 0.0,
            'total_gross_profit': 0.0,
            'total_gross_loss': 0.0
        }

    def _calculate_max_drawdown(self, equity_values: List[float]) -> float:
        """计算最大回撤"""
        peak = equity_values[0]
        max_drawdown = 0.0

        for equity in equity_values:
            if equity > peak:
                peak = equity
            drawdown = (peak - equity) / peak
            max_drawdown = max(max_drawdown, drawdown)

        return max_drawdown

    def _calculate_sharpe_ratio(self, equity_values: List[float]) -> float:
        """计算夏普比率"""
        if len(equity_values) <= 1:
            return 0.0

        returns = np.diff(equity_values) / equity_values[:-1]
        if np.std(returns) == 0:
            return 0.0

        return np.mean(returns) / np.std(returns) * np.sqrt(252)

    def _calculate_calmar_ratio(self, total_return: float, max_drawdown: float) -> float:
        """计算Calmar比率"""
        if max_drawdown == 0:
            return float('inf') if total_return > 0 else 0.0

        # 年化收益率 / 最大回撤
        annualized_return = total_return  # 假设数据已经是年化的
        return annualized_return / max_drawdown

    def _calculate_sortino_ratio(self) -> float:
        """计算索提诺比率 (Sortino Ratio)"""
        if len(self.equity_curve) < 2:
            return 0.0

        # 计算收益率序列
        equity_values = [equity for _, equity in self.equity_curve]
        returns = []
        for i in range(1, len(equity_values)):
            ret = (equity_values[i] - equity_values[i-1]) / equity_values[i-1]
            returns.append(ret)

        if len(returns) == 0:
            return 0.0

        # 计算平均收益率
        mean_return = np.mean(returns)

        # 计算下行偏差 (只考虑负收益)
        downside_returns = [ret for ret in returns if ret < 0]

        if len(downside_returns) == 0:
            # 没有负收益，索提诺比率为无穷大，返回一个大值
            return 100.0 if mean_return > 0 else 0.0

        downside_deviation = np.std(downside_returns)

        if downside_deviation == 0:
            return 0.0

        # 年化处理 (假设每个数据点代表一个交易日，一年252个交易日)
        annualized_return = mean_return * 252
        annualized_downside_deviation = downside_deviation * np.sqrt(252)

        return annualized_return / annualized_downside_deviation

    def _calculate_trade_statistics(self) -> Dict[str, Any]:
        """计算交易统计"""
        if not self.trades:
            return {
                'total_trades': 0,
                'win_rate': 0.0,
                'profit_loss_ratio': 0.0,
                'profitable_trades': 0,
                'losing_trades': 0
            }

        # 只统计平仓交易（卖出），买入开仓不计入盈亏统计
        closing_trades = [t for t in self.trades if t.side == 'sell']
        profitable_trades = [t for t in closing_trades if t.pnl > 0]
        losing_trades = [t for t in closing_trades if t.pnl < 0]

        win_rate = len(profitable_trades) / len(closing_trades) if len(closing_trades) > 0 else 0

        total_profit = sum(t.pnl for t in profitable_trades)
        total_loss = abs(sum(t.pnl for t in losing_trades))
        profit_loss_ratio = total_profit / total_loss if total_loss > 0 else float('inf')

        return {
            'total_trades': len(self.trades),  # 包含所有交易（买入+卖出）
            'closing_trades': len(closing_trades),  # 只有平仓交易
            'win_rate': win_rate,
            'profit_loss_ratio': profit_loss_ratio,
            'profitable_trades': len(profitable_trades),
            'losing_trades': len(losing_trades)
        }

    def _calculate_holding_time_statistics(self) -> Dict[str, Any]:
        """计算持仓时间统计"""
        # 获取所有卖出交易的持仓时间
        sell_trades_with_holding_time = [
            t for t in self.trades
            if t.side == 'sell' and t.holding_time is not None
        ]

        if not sell_trades_with_holding_time:
            return {
                'avg_holding_time_seconds': 0.0,
                'max_holding_time_seconds': 0.0,
                'min_holding_time_seconds': 0.0,
                'median_holding_time_seconds': 0.0
            }

        # 转换为秒
        holding_times_seconds = [t.holding_time / 1000 for t in sell_trades_with_holding_time]

        return {
            'avg_holding_time_seconds': np.mean(holding_times_seconds),
            'max_holding_time_seconds': np.max(holding_times_seconds),
            'min_holding_time_seconds': np.min(holding_times_seconds),
            'median_holding_time_seconds': np.median(holding_times_seconds)
        }

    def _calculate_pnl_statistics(self) -> Dict[str, Any]:
        """计算PnL统计"""
        if not self.trades:
            return {
                'avg_profit_per_trade': 0.0,
                'max_profit_per_trade': 0.0,
                'max_loss_per_trade': 0.0,
                'profit_factor': 0.0,
                'total_gross_profit': 0.0,
                'total_gross_loss': 0.0,
                'avg_profit_per_winning_trade': 0.0,
                'avg_loss_per_losing_trade': 0.0
            }

        # 只统计平仓交易的PnL
        closing_trades = [t for t in self.trades if t.side == 'sell']
        pnls = [t.pnl for t in closing_trades]
        profitable_pnls = [pnl for pnl in pnls if pnl > 0]
        losing_pnls = [pnl for pnl in pnls if pnl < 0]

        total_gross_profit = sum(profitable_pnls) if profitable_pnls else 0.0
        total_gross_loss = abs(sum(losing_pnls)) if losing_pnls else 0.0

        profit_factor = total_gross_profit / total_gross_loss if total_gross_loss > 0 else float('inf')

        return {
            'avg_profit_per_trade': np.mean(pnls),
            'max_profit_per_trade': np.max(pnls) if pnls else 0.0,
            'max_loss_per_trade': np.min(pnls) if pnls else 0.0,
            'profit_factor': profit_factor,
            'total_gross_profit': total_gross_profit,
            'total_gross_loss': total_gross_loss,
            'avg_profit_per_winning_trade': np.mean(profitable_pnls) if profitable_pnls else 0.0,
            'avg_loss_per_losing_trade': np.mean(losing_pnls) if losing_pnls else 0.0
        }



def load_data(data_path: str, symbol: str = None, snapshot_only: bool = True) -> pd.DataFrame:
    """
    加载回测数据 - 支持新的数据结构，优先使用snapshot数据

    Args:
        data_path: 数据路径，可以是：
                  - 旧格式：直接的数据目录路径
                  - 新格式：基础数据目录路径
        symbol: 标的代码（新格式需要）
        snapshot_only: 是否只使用snapshot数据（默认True）
    """
    # 尝试使用新的数据管理器
    try:
        from data_manager import DataManager

        # 如果指定了symbol，使用新格式
        if symbol:
            manager = DataManager()
            manager.config.base_dir = data_path

            # 如果要求只使用snapshot数据，优先尝试加载snapshot数据
            if snapshot_only:
                # try:
                #     data = manager.load_data(symbol, "snapshot")
                #     print(f"📊 使用snapshot数据: {len(data)} 行")
                #     return data
                # except:
                #     # 如果没有snapshot数据，使用merged数据（会在run_backtest中过滤）
                data = manager.load_data(symbol, "merged")
                print(f"📊 加载merged数据: {len(data)} 行 (将在回测中过滤snapshot)")
                return data
            else:
                # 不限制数据类型，使用merged数据
                data = manager.load_data(symbol, "merged")
                print(f"📊 使用所有merged数据: {len(data)} 行")
                return data

        # 检查是否是新格式的数据目录
        if os.path.exists(os.path.join(data_path, "config.json")):
            manager = DataManager()
            manager.config.base_dir = data_path

            # 使用默认标的或第一个可用标的
            symbols = manager.list_symbols()
            if symbols:
                default_symbol = manager.config.default_symbol if manager.config.default_symbol in symbols else symbols[0]
                print(f"📊 使用标的: {default_symbol}")

                # 如果要求只使用snapshot数据，优先尝试加载snapshot数据
                if snapshot_only:
                    data = manager.load_data(default_symbol, "merged")
                    print(f"📊 加载merged数据: {len(data)} 行 (将在回测中过滤snapshot)")
                    return data
                else:
                    # 不限制数据类型，使用merged数据
                    data = manager.load_data(default_symbol, "merged")
                    print(f"📊 使用所有merged数据: {len(data)} 行")
                    return data
            else:
                raise FileNotFoundError("新格式数据目录中没有找到任何标的")

    except ImportError:
        pass  # 如果没有data_manager，使用旧格式
    except Exception as e:
        print(f"⚠️ 新格式加载失败，尝试旧格式: {e}")

    # 使用旧格式加载
    data_files = []
    for file in os.listdir(data_path):
        if file.endswith('.csv'):
            data_files.append(os.path.join(data_path, file))

    data_files.sort()  # 按文件名排序

    all_data = []
    for file in data_files:
        df = pd.read_csv(file)
        all_data.append(df)

    combined_data = pd.concat(all_data, ignore_index=True)
    combined_data = combined_data.sort_values('timestamp').reset_index(drop=True)

    print(f"✅ 加载数据完成: {len(combined_data)} 行")
    return combined_data
