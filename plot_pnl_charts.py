#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成PnL分析图表 - 修复中文显示
"""

import json
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
import seaborn as sns
import numpy as np
import os
from datetime import datetime
from backtest_engine import BacktestEngine, BacktestConfig, load_data
from strategy_base import strategy_manager
import strategy_qmt_mm

# 设置英文字体和样式
def setup_fonts():
    """设置字体和样式"""
    # 使用默认英文字体
    matplotlib.rcParams['font.family'] = 'sans-serif'
    matplotlib.rcParams['axes.unicode_minus'] = False

    # 设置样式
    try:
        plt.style.use('seaborn-v0_8')
    except:
        try:
            plt.style.use('seaborn')
        except:
            pass  # 使用默认样式

    print("✅ Font configuration completed")

def load_best_config():
    """加载最佳配置"""
    try:
        with open('optimization_results/qmt_mm_best_config.json', 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        return config_data['best_params']
    except:
        print("⚠️ 未找到最佳配置，使用默认配置")
        return {}

def run_backtest_for_charts():
    """运行回测获取详细数据"""
    print("🚀 运行回测获取图表数据...")
    
    # 加载最佳配置
    best_params = load_best_config()
    print(f"📊 使用参数: {len(best_params)} 个")
    
    # 加载数据
    data = load_data("backtest_data", "ETF_513120", snapshot_only=True)
    print(f"📊 数据量: {len(data)} 行")
    
    # 创建策略
    strategy = strategy_manager.create_strategy('qmt_mm', best_params)
    
    # 创建回测配置
    config = BacktestConfig()
    config.initial_cash = 1000000.0
    config.commission_rate = 0.00005  # 万0.5手续费
    config.snapshot_only = True
    
    # 运行回测
    engine = BacktestEngine(config, strategy)
    results = engine.run_backtest(data)
    
    print(f"✅ 回测完成")
    print(f"   总收益率: {results.get('total_return', 0)*100:.2f}%")
    print(f"   夏普比率: {results.get('sharpe_ratio', 0):.4f}")
    print(f"   总交易次数: {results.get('total_trades', 0)}")
    
    return results

def plot_pnl_analysis_fixed(results):
    """绘制修复版PnL分析图"""
    print("📈 生成修复版PnL分析图...")
    
    # 确保结果目录存在
    os.makedirs("optimization_results", exist_ok=True)
    
    # 获取权益曲线数据
    equity_curve = results.get('equity_curve', [])
    if not equity_curve:
        print("⚠️ 没有权益曲线数据")
        return
    
    # 转换权益曲线数据
    equity_data = []
    for item in equity_curve:
        if isinstance(item, tuple) and len(item) == 2:
            timestamp, equity = item
            equity_data.append({'timestamp': timestamp, 'equity': equity})
        elif isinstance(item, dict):
            equity_data.append(item)
    
    if not equity_data:
        print("⚠️ 权益数据格式错误")
        return
    
    equity_df = pd.DataFrame(equity_data)
    equity_df['datetime'] = pd.to_datetime(equity_df['timestamp'], unit='ms')
    equity_df = equity_df.set_index('datetime')
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('QMT Strategy PnL Analysis Report', fontsize=16, fontweight='bold')

    # 1. Equity Curve
    ax1 = axes[0, 0]
    ax1.plot(equity_df.index, equity_df['equity'], 'b-', linewidth=2, label='Total Equity')
    ax1.axhline(y=1000000, color='r', linestyle='--', alpha=0.7, label='Initial Capital')
    ax1.set_title('Equity Curve')
    ax1.set_ylabel('Equity (CNY)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.tick_params(axis='x', rotation=45)

    # 2. Returns Curve
    ax2 = axes[0, 1]
    returns = equity_df['equity'].pct_change().fillna(0)
    cumulative_returns = (1 + returns).cumprod() - 1
    ax2.plot(equity_df.index, cumulative_returns * 100, 'g-', linewidth=2)
    ax2.set_title('Cumulative Returns')
    ax2.set_ylabel('Returns (%)')
    ax2.grid(True, alpha=0.3)
    ax2.tick_params(axis='x', rotation=45)

    # 3. Drawdown Analysis
    ax3 = axes[1, 0]
    peak = equity_df['equity'].expanding().max()
    drawdown = (equity_df['equity'] - peak) / peak
    ax3.fill_between(equity_df.index, drawdown * 100, 0, color='red', alpha=0.3)
    ax3.plot(equity_df.index, drawdown * 100, 'r-', linewidth=1)
    ax3.set_title('Drawdown Analysis')
    ax3.set_ylabel('Drawdown (%)')
    ax3.grid(True, alpha=0.3)
    ax3.tick_params(axis='x', rotation=45)
    
    # 4. 关键指标汇总
    ax4 = axes[1, 1]
    ax4.axis('off')  # 隐藏坐标轴
    
    # 计算关键指标
    total_return = results.get('total_return', 0) * 100
    sharpe_ratio = results.get('sharpe_ratio', 0)
    max_drawdown = results.get('max_drawdown', 0) * 100
    total_trades = results.get('total_trades', 0)
    win_rate = results.get('win_rate', 0) * 100
    final_equity = results.get('final_equity', 0)
    
    # 创建指标文本
    metrics_text = f"""
Key Performance Metrics

Total Return: {total_return:.2f}%
Sharpe Ratio: {sharpe_ratio:.4f}
Max Drawdown: {max_drawdown:.2f}%
Total Trades: {total_trades}
Win Rate: {win_rate:.2f}%
Final Equity: {final_equity:,.0f} CNY

Strategy Assessment:
{'Good Performance' if total_return > 0 else 'Needs Optimization'}
"""

    ax4.text(0.1, 0.9, metrics_text, transform=ax4.transAxes, fontsize=12,
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    ax4.set_title('Performance Summary')
    
    plt.tight_layout()
    
    # 保存图表
    save_path = "optimization_results/pnl_analysis_fixed.png"
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"✅ PnL Analysis Chart saved: {save_path}")

    plt.close()  # 关闭图表释放内存

def plot_trade_analysis_fixed(results):
    """绘制修复版交易分析图"""
    print("📊 生成修复版交易分析图...")
    
    trades = results.get('trades', [])
    if not trades:
        print("⚠️ 没有交易数据")
        return
    
    # 处理交易数据
    trades_data = []
    for i, trade in enumerate(trades):
        if hasattr(trade, '__dict__'):
            trade_dict = trade.__dict__.copy()
            trade_dict['trade_id'] = i
            trades_data.append(trade_dict)
        elif isinstance(trade, dict):
            trade['trade_id'] = i
            trades_data.append(trade)
        else:
            trades_data.append({'trade_id': i, 'pnl': 0, 'quantity': 0})
    
    if not trades_data:
        print("⚠️ 交易数据为空")
        return
    
    trades_df = pd.DataFrame(trades_data)
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('QMT Strategy Trade Analysis Report', fontsize=16, fontweight='bold')

    # 1. Trade PnL Distribution
    ax1 = axes[0, 0]
    if 'pnl' in trades_df.columns:
        pnl_values = trades_df['pnl'].values
        colors = ['green' if pnl > 0 else 'red' if pnl < 0 else 'gray' for pnl in pnl_values]
        bars = ax1.bar(range(len(pnl_values)), pnl_values, color=colors, alpha=0.7)
        ax1.axhline(y=0, color='black', linestyle='-', linewidth=1)
        ax1.set_title('Individual Trade PnL Distribution')
        ax1.set_xlabel('Trade Number')
        ax1.set_ylabel('PnL (CNY)')
        ax1.grid(True, alpha=0.3)
    else:
        ax1.text(0.5, 0.5, 'No PnL Data', ha='center', va='center', transform=ax1.transAxes)
        ax1.set_title('Individual Trade PnL Distribution')

    # 2. Cumulative PnL
    ax2 = axes[0, 1]
    if 'pnl' in trades_df.columns:
        pnl_values = trades_df['pnl'].values
        cumulative_pnl = np.cumsum(pnl_values)
        ax2.plot(range(len(cumulative_pnl)), cumulative_pnl, 'b-', linewidth=2, marker='o', markersize=3)
        ax2.set_title('Cumulative PnL Curve')
        ax2.set_xlabel('Trade Number')
        ax2.set_ylabel('Cumulative PnL (CNY)')
        ax2.grid(True, alpha=0.3)
    else:
        ax2.text(0.5, 0.5, 'No PnL Data', ha='center', va='center', transform=ax2.transAxes)
        ax2.set_title('Cumulative PnL Curve')

    # 3. Trade Size Distribution
    ax3 = axes[1, 0]
    if 'quantity' in trades_df.columns:
        quantities = trades_df['quantity'].values
        ax3.hist(quantities, bins=20, alpha=0.7, color='orange', edgecolor='black')
        ax3.set_title('Trade Size Distribution')
        ax3.set_xlabel('Trade Quantity')
        ax3.set_ylabel('Frequency')
        ax3.grid(True, alpha=0.3)
    else:
        ax3.text(0.5, 0.5, 'No Trade Size Data', ha='center', va='center', transform=ax3.transAxes)
        ax3.set_title('Trade Size Distribution')
    
    # 4. 交易统计汇总
    ax4 = axes[1, 1]
    ax4.axis('off')
    
    # 计算交易统计
    if 'pnl' in trades_df.columns:
        pnl_values = trades_df['pnl'].values
        profit_trades = len([pnl for pnl in pnl_values if pnl > 0])
        loss_trades = len([pnl for pnl in pnl_values if pnl < 0])
        zero_trades = len([pnl for pnl in pnl_values if pnl == 0])
        
        total_profit = sum([pnl for pnl in pnl_values if pnl > 0])
        total_loss = sum([pnl for pnl in pnl_values if pnl < 0])
        
        stats_text = f"""
Trade Statistics Summary

Total Trades: {len(pnl_values)}
Profitable Trades: {profit_trades}
Loss Trades: {loss_trades}
Breakeven Trades: {zero_trades}

Win Rate: {profit_trades/len(pnl_values)*100:.1f}%
Total Profit: {total_profit:.2f} CNY
Total Loss: {total_loss:.2f} CNY
Net PnL: {total_profit + total_loss:.2f} CNY
"""
    else:
        stats_text = "No Trade Statistics Data"

    ax4.text(0.1, 0.9, stats_text, transform=ax4.transAxes, fontsize=12,
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
    ax4.set_title('Trade Statistics Summary')
    
    plt.tight_layout()
    
    # 保存图表
    save_path = "optimization_results/trade_analysis_fixed.png"
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"✅ Trade Analysis Chart saved: {save_path}")

    plt.close()  # 关闭图表释放内存

def main():
    """主函数"""
    print("🎨 生成修复版PnL和交易分析图表")
    print("=" * 60)
    
    # 设置字体
    setup_fonts()
    
    try:
        # 运行回测获取数据
        results = run_backtest_for_charts()
        
        # 生成修复版图表
        plot_pnl_analysis_fixed(results)
        plot_trade_analysis_fixed(results)
        
        print(f"\n🎉 修复版图表生成完成！")
        print(f"📁 保存位置:")
        print(f"   - optimization_results/pnl_analysis_fixed.png")
        print(f"   - optimization_results/trade_analysis_fixed.png")
        
    except Exception as e:
        print(f"❌ 生成图表失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
